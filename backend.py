from flask import Flask, request, jsonify
from flask_cors import CORS
import json

app = Flask(__name__)
CORS(app)  # 启用CORS，允许前端跨域访问

# 用于存储数据的全局变量
allinfo_dict = {}

def print_debug_info():
    print("\n=== 当前存储的 allinfo_dict ===")
    print(json.dumps(allinfo_dict, ensure_ascii=False, indent=2))
    print("==============================\n")

@app.route('/save_allinfo', methods=['POST'])
def save_allinfo():
    try:
        data = request.get_json()
        if 'allinfo' not in data:
            return jsonify({'error': '缺少allinfo字段'}), 400
        
        global allinfo_dict
        allinfo_dict = data['allinfo']
        print("\n=== 收到保存请求 ===")
        print_debug_info()
        return jsonify({'message': '数据保存成功'}), 200
    except Exception as e:
        print(f"\n=== 保存出错: {str(e)} ===")
        return jsonify({'error': str(e)}), 500

@app.route('/load_allinfo', methods=['POST'])
def load_allinfo():
    try:
        global allinfo_dict
        print("\n=== 收到读取请求 ===")
        print_debug_info()
        return jsonify({'allinfo': allinfo_dict}), 200
    except Exception as e:
        print(f"\n=== 读取出错: {str(e)} ===")
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    print("后端服务器启动在 http://localhost:5000")
    print("等待请求中...")
    app.run(debug=True, port=5000) 