# Task Name Functionality Test Guide

## Overview
This document outlines the task name modification functionality that has been implemented in the Vue.js application.

## Features Implemented

### 1. Default Task Name
- ✅ Default task name is set to "未命名" (Unnamed) when a new task/session is created
- ✅ Task name is stored in the Pinia store and persists during the session
- ✅ Task name is displayed in the HeaderBar component

### 2. Task Name Display
- ✅ Task name is shown in the HeaderBar below the main title
- ✅ Only visible on guided pages (pageName == '引导式')
- ✅ Styled with a subtle background and border

### 3. Save Task Button Functionality
- ✅ "保存任务" (Save Task) button in HeaderBar triggers task name modal
- ✅ Modal allows user to modify task name before saving
- ✅ Validation prevents empty task names
- ✅ Character limit of 50 characters enforced

### 4. Browser Close Event Handling
- ✅ beforeunload event listener added to App.vue
- ✅ Modal appears when user tries to close browser on guided pages
- ✅ Provides option to save task with custom name before closing

### 5. TaskNameModal Component
- ✅ Reusable modal component with proper styling
- ✅ Input validation and error handling
- ✅ Responsive design for mobile devices
- ✅ Keyboard shortcuts (Enter to confirm, Escape to cancel)
- ✅ Loading states during save operations

## Testing Instructions

### Test 1: Default Task Name Display
1. Navigate to http://localhost:5175/
2. Verify that "任务: 未命名" appears below the main title in the header
3. ✅ Expected: Task name should be visible and show "未命名"

### Test 2: Save Task Button
1. Click the "保存任务" button in the HeaderBar
2. Verify that the TaskNameModal appears
3. Try entering different task names:
   - Valid name (e.g., "测试任务1")
   - Empty name (should show error)
   - Very long name (should show character limit error)
4. Click "确认" to save
5. ✅ Expected: Task name should update in the header display

### Test 3: Task Name Validation
1. Open the task name modal
2. Try these scenarios:
   - Leave input empty and click confirm → Should show "任务名称不能为空"
   - Enter more than 50 characters → Should show character limit error
   - Enter valid name → Should save successfully
3. ✅ Expected: Proper validation messages and behavior

### Test 4: Browser Close Event
1. Make some changes in the application
2. Try to close the browser tab/window
3. Verify that the task name modal appears
4. Test both "确认" and "取消" options
5. ✅ Expected: Modal should appear and handle both save and cancel scenarios

### Test 5: Modal Interactions
1. Open task name modal
2. Test these interactions:
   - Click outside modal → Should close modal
   - Press Escape key → Should close modal
   - Press Enter key → Should confirm if input is valid
   - Click close button (X) → Should close modal
3. ✅ Expected: All interaction methods should work properly

### Test 6: Responsive Design
1. Open task name modal on different screen sizes
2. Test on mobile viewport (< 640px)
3. ✅ Expected: Modal should adapt to smaller screens with stacked buttons

## Technical Implementation Details

### Files Modified/Created:
1. `src/store/states.js` - Added task name state management
2. `src/components/common/TaskNameModal.vue` - New modal component
3. `src/components/HeaderBar.vue` - Updated to show task name and handle save
4. `src/App.vue` - Added browser close event handling
5. `src/views/InductiveView.vue` - Added task name initialization

### Key Functions:
- `setTaskName(name)` - Updates task name in store
- `clearTaskName()` - Resets task name to default
- `handleBeforeUnload()` - Handles browser close events
- `handleTaskNameConfirm()` - Processes task name confirmation
- `validateInput()` - Validates task name input

### Styling Patterns:
- Consistent with existing modal patterns (SystemErrorMask)
- Uses application color scheme and typography
- Responsive design with mobile-first approach
- Smooth animations and transitions

## Known Limitations

1. Task name is not persisted across browser sessions (only in memory)
2. Browser close event may not work consistently across all browsers
3. Task name is only shown on guided pages (pageName == '引导式')

## Future Enhancements

1. Persist task name in localStorage for session recovery
2. Add task name editing directly in the header (click to edit)
3. Add task name history/suggestions
4. Integrate with backend task management system
5. Add task name templates or categories
