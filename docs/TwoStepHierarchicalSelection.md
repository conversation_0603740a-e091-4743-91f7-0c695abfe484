# Two-Step Hierarchical Selection Implementation

## Overview

This document describes the implementation of the two-step hierarchical selection pattern for index and dimension selection, replacing the previous single-step approach that fetched all data at once.

## Changes Made

### 1. Updated States Store (`src/store/states.js`)

**Added new properties:**
- `availableCategories`: Stores category hierarchy data
- `selectedCategories`: Tracks selected categories for indicators and dimensions

**Added new functions:**
- `setAvailableCategories()`: Store category data
- `clearAvailableCategories()`: Clear category data
- `setSelectedCategories()`: Update selected category state
- `clearSelectedCategories()`: Clear selected categories

### 2. Created Category API Utilities (`src/utils/categoryApi.js`)

**New API functions:**
- `fetchCategories(key)`: Fetch available categories for a granularity
- `fetchIndicesByCategory(key, category_lv_1, category_lv_2, category_lv_3)`: Fetch indices for selected category
- `fetchDimensionsByCategory(key, category_lv_1, category_lv_2, category_lv_3, indices)`: Fetch dimensions for selected category

**Features:**
- GET requests with query parameters (per user preference)
- Proper error handling with retry functionality
- Mock data fallbacks for development
- ID values used for API calls (key_id parameter)

### 3. Updated IndexSelector Component (`src/components/IndexSelector.vue`)

**Two-step selection pattern:**
1. **Step 1**: Fetch and display categories when component mounts
2. **Step 2**: Fetch indices when user selects a category

**Key changes:**
- Removed category organization from indicator data
- Added loading states for categories and indices
- Category selection triggers API call to fetch indices
- Proper parameter passing: `key`, `category_lv_1`, `category_lv_2`, `category_lv_3`
- Data stored in centralized states.js store

### 4. Updated DimensionSelector Component (`src/components/DimensionSelector.vue`)

**Same two-step pattern as IndexSelector:**
1. **Step 1**: Fetch and display categories
2. **Step 2**: Fetch dimensions based on selected category and indicators

**Additional features:**
- Watches for indicator changes to refetch dimensions
- Includes selected indicator IDs in dimension API calls
- Proper category parameter handling

### 5. Updated LeftColumn Component (`src/components/LeftColumn.vue`)

**Removed old implementation:**
- Removed `fetchIndicators()` and `fetchDimensions()` functions
- Removed hardcoded category parameters (`category_lv_1: '总公司'`)
- Removed function calls from `onMounted` and watch functions
- Cleaned up unused imports and variables

**Integration with new pattern:**
- Floating window components now handle all data fetching
- LeftColumn focuses on UI state management only

## API Integration

### Expected API Endpoints

1. **GET `/api/v1/config/data_prep/categories`**
   - Parameters: `key` (granularity ID)
   - Returns: Hierarchical category structure

2. **GET `/api/v1/config/data_prep/indices`**
   - Parameters: `key`, `category_lv_1`, `category_lv_2`, `category_lv_3`
   - Returns: Filtered indices for selected category

3. **GET `/api/v1/config/data_prep/dimensions`**
   - Parameters: `key`, `category_lv_1`, `category_lv_2`, `category_lv_3`, `indices`
   - Returns: Filtered dimensions for selected category and indicators

### Data Flow

1. User selects granularity → Categories are fetched and displayed
2. User selects category → Indices/dimensions are fetched for that category
3. All data is stored in centralized states.js store
4. Components react to state changes automatically

## Benefits

1. **Performance**: Only fetch data when needed, reducing initial load time
2. **User Experience**: Clear hierarchical navigation with loading states
3. **Scalability**: Can handle large category hierarchies efficiently
4. **Maintainability**: Centralized data management and clear separation of concerns
5. **Flexibility**: Easy to extend with additional category levels

## Testing Validation

The implementation includes:
- ✅ Proper error handling with fallback mock data
- ✅ Loading states for better UX
- ✅ Centralized state management
- ✅ GET requests with query parameters
- ✅ ID values used for API calls
- ✅ Floating window interface maintained
- ✅ Two-step selection pattern for both indicators and dimensions

## Next Steps

1. Test with actual API endpoints when available
2. Verify category data structure matches expected format
3. Add unit tests for the new API functions
4. Monitor performance with large datasets
5. Consider adding caching for frequently accessed categories
