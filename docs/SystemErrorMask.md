# System Error Mask Documentation

## Overview

The System Error Mask is a full-screen overlay component that displays only for server-side system errors (HTTP 5xx status codes), distinguishing them from client-side validation errors and success notifications.

## Features

### Error Classification
The system automatically classifies errors into three categories:

1. **System Errors** (显示错误遮罩)
   - HTTP 5xx status codes (500, 502, 503, 504, etc.)
   - Network connectivity issues
   - API timeout errors
   - Critical system failures

2. **User Errors** (显示提示消息)
   - HTTP 4xx status codes (400, 401, 403, 404, etc.)
   - Validation errors
   - User input errors
   - Permission denied errors

3. **Success Messages** (显示成功提示)
   - Operation completed successfully
   - Data saved/created/updated messages

### Components

#### SystemErrorMask.vue
- Full-screen overlay with high z-index (9999)
- User-friendly error messaging
- Recovery options (retry, refresh, contact support)
- Expandable error details
- Non-dismissible background (users must choose an action)

#### errorHandler.js
- Error classification utility
- Automatic error type detection
- Centralized error handling logic

## Usage

### Basic Integration

1. **Import the error handler utility:**
```javascript
import { handleError } from '@/utils/errorHandler'
```

2. **Use in try-catch blocks:**
```javascript
try {
  const response = await axios.post('/api/endpoint', data)
  // Handle success
} catch (error) {
  const retryAction = () => fetchData() // Optional retry function
  handleError(error, 'Custom error message', retryAction, showToast)
}
```

### Error Classification Examples

#### System Errors (Show Mask)
```javascript
// HTTP 500 Internal Server Error
const systemError = {
  response: { status: 500, data: { message: 'Internal server error' } }
}
handleError(systemError, '数据处理失败', retryAction, showToast)

// Network Error
const networkError = {
  request: {}, // No response indicates network error
}
handleError(networkError, '网络连接失败', retryAction, showToast)
```

#### User Errors (Show Toast)
```javascript
// HTTP 400 Bad Request
const userError = {
  response: { status: 400, data: { message: 'Invalid input' } }
}
handleError(userError, '请完善以下必填项', null, showToast)

// Validation Error
handleError(null, '请输入有效的邮箱地址', null, showToast)
```

#### Success Messages (Show Toast)
```javascript
handleError(null, '表格已创建成功', null, showToast)
handleError(null, '文件上传成功', null, showToast)
```

## Configuration

### Global Integration
The SystemErrorMask is automatically included in the main App.vue:

```vue
<template>
  <div class="layout-wrapper">
    <!-- Other components -->
    <SystemErrorMask />
  </div>
</template>
```

### State Management
Error state is managed through Pinia store (`src/store/states.js`):

```javascript
// Show system error
statesStore.showSystemError(title, message, errorCode, retryAction)

// Hide system error
statesStore.hideSystemError()
```

## Testing

Use the ErrorTestComponent to test different error scenarios:

1. Navigate to the DataSheet view
2. Click the "错误测试" button in the feature bar
3. Test different error types:
   - System errors (500, 502, 503, network)
   - User errors (400, 401, validation)
   - Success messages

## Customization

### Error Messages
Customize error messages in `src/utils/errorHandler.js`:

```javascript
function getSystemErrorMessage(error, customMessage) {
  // Add custom logic for specific error types
  if (error?.response?.status === 500) {
    return '服务器遇到内部错误，请稍后重试或联系技术支持。'
  }
  // ... other cases
}
```

### Error Patterns
Add new error patterns to classify errors:

```javascript
const SYSTEM_ERROR_PATTERNS = {
  statusCodes: [500, 501, 502, 503, 504, 505, 506, 507, 508, 509, 510, 511],
  networkErrors: ['NETWORK_ERROR', 'TIMEOUT_ERROR', 'CONNECTION_REFUSED'],
  systemFailures: ['INTERNAL_SERVER_ERROR', 'SERVICE_UNAVAILABLE']
}
```

### UI Styling
Customize the error mask appearance in `SystemErrorMask.vue`:

```css
.system-error-mask {
  background-color: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(4px);
  z-index: 9999;
}
```

## Best Practices

1. **Always provide retry actions** for system errors when possible
2. **Use descriptive error messages** that help users understand what went wrong
3. **Classify errors correctly** to ensure appropriate UI response
4. **Test error scenarios** regularly using the ErrorTestComponent
5. **Monitor error patterns** to improve system reliability

## Integration Examples

### API Calls
```javascript
// In InductiveView.vue
try {
  const resp = await axios.post('/api/v1/config/fetch_data', payload)
  // Handle success
} catch (e) {
  const retryAction = () => fetchData()
  handleError(e, '数据获取失败', retryAction, showToast)
}
```

### Login Flow
```javascript
// In LoginView.vue
try {
  const response = await axios.post('/api/v1/accounts/auth/login', requestPayload)
  // Handle success
} catch (err) {
  const retryAction = () => onLogin()
  handleError(err, '登录失败', retryAction, showToast)
}
```

### Data Fetching
```javascript
// In LeftColumn.vue
try {
  const resp = await axios.get('/api/v1/config/data_prep/indices', { params, headers })
  // Handle success
} catch (error) {
  const retryAction = () => fetchIndicators()
  handleError(error, '获取指标数据失败', retryAction)
}
```

## Troubleshooting

### Common Issues

1. **Error mask not showing for system errors**
   - Check error classification in browser console
   - Verify error status codes and patterns
   - Ensure SystemErrorMask is included in App.vue

2. **User errors showing mask instead of toast**
   - Review error message patterns in errorHandler.js
   - Check if showToast function is passed correctly
   - Verify error classification logic

3. **Retry action not working**
   - Ensure retry function is properly defined
   - Check for async/await handling in retry logic
   - Verify error state is cleared after successful retry

### Debug Mode
Enable debug logging by adding console.log in errorHandler.js:

```javascript
export function handleError(error, message = '', retryAction = null, showToast = null) {
  const errorType = classifyError(error, message)
  
  console.log('Error classification:', {
    error,
    message,
    type: errorType,
    status: error?.response?.status,
    code: error?.code
  })
  
  // ... rest of function
}
```
