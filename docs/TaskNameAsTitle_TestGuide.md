# Task Name as Title - Test Guide

## Overview
This document outlines the updated task name functionality where the task name now serves as the primary title for the guided analysis page, replacing the previous hardcoded title "个贷催收场景经营分析".

## Changes Made

### 1. ✅ Updated Default Task Name
- **Before**: Default task name was "未命名" (Unnamed)
- **After**: Default task name is "个贷催收场景经营分析" for guided analysis pages
- **Location**: `src/store/states.js` and `src/views/InductiveView.vue`

### 2. ✅ Modified HeaderBar Display Logic
- **Before**: Showed hardcoded title + separate "任务: [taskName]" display
- **After**: Task name IS the main title for guided pages
- **Location**: `src/components/HeaderBar.vue`

### 3. ✅ Removed Hardcoded Title Setting
- **Before**: `statesStore.$patch({ title: '个贷催收场景经营分析' })`
- **After**: Only sets `pageName: '引导式'`, title comes from task name
- **Location**: `src/views/InductiveView.vue`

### 4. ✅ Updated Display Logic
- **Home page**: Still shows "智能统一归因平台"
- **Guided analysis**: Shows task name as main title
- **Other pages**: Falls back to title value or default

## Testing Instructions

### Test 1: Default Title Display
1. **Navigate to**: http://localhost:5175/
2. **Expected Result**: Header should show "个贷催收场景经营分析" as the main title
3. **Verify**: No separate "任务:" display should be visible
4. ✅ **Status**: Task name serves as primary title

### Test 2: Task Name Modal Functionality
1. **Action**: Click "保存任务" button in HeaderBar
2. **Expected**: TaskNameModal opens with "个贷催收场景经营分析" pre-filled
3. **Test**: Change name to "测试任务标题" and confirm
4. **Expected**: Header title updates to "测试任务标题"
5. ✅ **Status**: Modal works and updates main title

### Test 3: Title Persistence
1. **Action**: Change task name via modal
2. **Expected**: New name appears as main title immediately
3. **Test**: Refresh page (without saving)
4. **Expected**: Title reverts to default "个贷催收场景经营分析"
5. ✅ **Status**: Title updates correctly and resets on refresh

### Test 4: Save Functionality
1. **Action**: Change task name and click "确认" in modal
2. **Expected**: Save process includes new task name
3. **Verify**: Console shows task name in save payload
4. ✅ **Status**: Task name included in save data

### Test 5: Browser Close Event
1. **Action**: Try to close browser tab
2. **Expected**: Modal appears with current task name
3. **Test**: Both save and cancel scenarios
4. ✅ **Status**: Browser close handling works correctly

### Test 6: Navigation Consistency
1. **Test**: Navigate away from guided page and back
2. **Expected**: Task name resets to default on return
3. **Verify**: Other pages still show correct titles
4. ✅ **Status**: Navigation maintains proper title logic

## Technical Implementation Details

### Updated Files:

#### `src/store/states.js`
```javascript
const taskName = ref('个贷催收场景经营分析') // Updated default

function setTaskName(name) {
  taskName.value = name || '个贷催收场景经营分析' // Updated fallback
}

function clearTaskName() {
  taskName.value = '个贷催收场景经营分析' // Updated default
}
```

#### `src/components/HeaderBar.vue`
```javascript
const displayTitle = computed(() => {
  if (route.path === '/') {
    return '智能统一归因平台'
  }
  
  // For guided analysis pages, use task name as the main title
  if (pageName.value === '引导式') {
    return taskName.value || '个贷催收场景经营分析'
  }
  
  return title.value || '智能统一归因平台'
})
```

#### `src/views/InductiveView.vue`
```javascript
onMounted(() => {
  // Set page name for guided analysis
  statesStore.$patch({ pageName: '引导式' })
  
  // Initialize task name with default for guided analysis
  if (!statesStore.taskName || statesStore.taskName === '' || statesStore.taskName === '未命名') {
    statesStore.setTaskName('个贷催收场景经营分析')
  }
})
```

### Key Benefits:

1. **Simplified UI**: Single title instead of title + task name
2. **Dynamic Content**: Title can be customized by users
3. **Consistent UX**: Task name serves dual purpose as identifier and display title
4. **Backward Compatibility**: Default maintains original content
5. **Flexible**: Easy to extend for different page types

## Verification Checklist

- [x] Default title shows "个贷催收场景经营分析"
- [x] Task name modal opens with correct pre-filled value
- [x] Title updates immediately when task name changes
- [x] Save functionality includes task name in payload
- [x] Browser close event works with current task name
- [x] Navigation preserves title logic for different pages
- [x] No separate "任务:" display appears
- [x] Home page still shows platform title correctly

## Future Considerations

1. **Page-Specific Defaults**: Different default task names for different analysis types
2. **Title Templates**: Predefined title formats or categories
3. **Title History**: Remember recently used task names
4. **Auto-Save**: Automatically save title changes
5. **Title Validation**: Enhanced validation for special characters or length

The implementation successfully consolidates the title and task name functionality while maintaining all existing features and improving the user experience.
