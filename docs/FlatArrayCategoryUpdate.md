# Flat Array Category Format Update

## Overview

Updated the category handling system to work with the new flat array response format from the API, replacing the previous hierarchical nested structure.

## API Response Format Change

### Previous Format (Hierarchical)
```json
[
  {
    "id": "cat1",
    "name": "总公司",
    "level": 1,
    "children": [
      {
        "id": "cat1-1", 
        "name": "子公司1",
        "level": 2,
        "children": [...]
      }
    ]
  }
]
```

### New Format (Flat Array)
```json
[
  {
    "id": "cat1",
    "name": "总公司", 
    "parent": null,
    "level": 1
  },
  {
    "id": "cat1-1",
    "name": "子公司1",
    "parent": "cat1", 
    "level": 2
  },
  {
    "id": "cat1-1-1",
    "name": "部门A",
    "parent": "cat1-1",
    "level": 3
  }
]
```

## Changes Made

### 1. Updated `src/utils/categoryApi.js`

**Mock Data Updated:**
- Changed from nested hierarchical structure to flat array
- Each category now has `parent` field instead of `children`
- Maintains `id`, `name`, `level` fields
- `parent: null` for root level categories

### 2. Updated `src/components/IndexSelector.vue`

**Computed Properties Changed:**
- `categories` → `level1Categories`, `level2Categories`, `level3Categories`
- Uses flat array filtering instead of nested traversal:
  - Level 1: `filter(cat => cat.level === 1)`
  - Level 2: `filter(cat => cat.level === 2 && cat.parent === selectedCat1)`
  - Level 3: `filter(cat => cat.level === 3 && cat.parent === selectedCat2)`

**Selection Logic Updated:**
- Selection functions now work with category IDs instead of names
- Added `getCategoryNameById()` helper function
- API calls still use category names (converted from IDs)
- Template uses `cat.id` for selection tracking

### 3. Updated `src/components/DimensionSelector.vue`

**Same Changes as IndexSelector:**
- Flat array filtering for category levels
- ID-based selection with name conversion for API calls
- Consistent parent-child relationship handling
- Maintains indicator dependency for dimension filtering

## Technical Implementation

### Category Display Logic

```javascript
// Level 1 Categories (Root)
const level1Categories = computed(() => {
  const allCategories = availableCategories.value || []
  return allCategories.filter(cat => cat.level === 1)
})

// Level 2 Categories (Children of selected Level 1)
const level2Categories = computed(() => {
  if (!selectedCat1.value) return []
  const allCategories = availableCategories.value || []
  return allCategories.filter(cat => 
    cat.level === 2 && cat.parent === selectedCat1.value
  )
})

// Level 3 Categories (Children of selected Level 2)
const level3Categories = computed(() => {
  if (!selectedCat2.value) return []
  const allCategories = availableCategories.value || []
  return allCategories.filter(cat => 
    cat.level === 3 && cat.parent === selectedCat2.value
  )
})
```

### ID to Name Conversion

```javascript
const getCategoryNameById = (categoryId) => {
  if (!categoryId) return ''
  const allCategories = availableCategories.value || []
  const category = allCategories.find(cat => cat.id === categoryId)
  return category ? category.name : ''
}
```

### API Call Integration

```javascript
// Convert IDs to names for API compatibility
const cat1Name = getCategoryNameById(selectedCat1.value)
const cat2Name = getCategoryNameById(selectedCat2.value)
const cat3Name = getCategoryNameById(selectedCat3.value)

// API call with names
const indices = await fetchIndicesByCategory(
  granularityId,
  cat1Name,
  cat2Name, 
  cat3Name
)
```

## Benefits

1. **Simplified Data Structure**: Flat array is easier to process and maintain
2. **Better Performance**: No need to traverse nested structures
3. **Flexible Hierarchy**: Easy to add/remove category levels
4. **Consistent API**: Backend can return simple flat structure
5. **Maintainable Code**: Clear parent-child relationships via IDs

## Backward Compatibility

- ✅ Same user interaction flow maintained
- ✅ API calls still use category names as expected
- ✅ Loading states and error handling preserved
- ✅ Two-step selection pattern unchanged
- ✅ Centralized state management continues to work

## Testing Checklist

- [ ] Level 1 categories display correctly
- [ ] Level 2 categories appear when Level 1 selected
- [ ] Level 3 categories appear when Level 2 selected
- [ ] Category selection triggers correct API calls
- [ ] API calls include proper category names
- [ ] Indices/dimensions load based on selected categories
- [ ] Loading states work during category and data fetching
- [ ] Error handling works with fallback mock data
- [ ] Both IndexSelector and DimensionSelector work consistently

## Migration Notes

If migrating from the old hierarchical format:
1. Backend should return flat array with `parent` and `level` fields
2. Frontend automatically handles the conversion
3. No changes needed to API parameter structure
4. Mock data updated to match new format
