import axios from 'axios'
import { handleError } from './errorHandler'

/**
 * Fetch available categories for hierarchical selection
 * @param {string} key - Granularity key/ID
 * @returns {Promise<Array>} Array of category objects
 */
export const fetchCategories = async (key) => {
  try {
    console.log('Fetching categories with key:', key)
    const response = await axios.get('/api/v1/config/data_prep/categories', {
      params: { key },
      headers: {
        token: localStorage.getItem('token'),
      },
    })
    
    console.log('Categories API response:', response.data)
    
    if (response.data.error_code === 0) {
      return response.data.data || []
    } else {
      console.error('Categories API error:', response.data.message)
      // Return mock data for development
      return getMockCategories()
    }
  } catch (error) {
    console.error('Failed to fetch categories:', error)
    
    // Use error handler for consistent error management
    const retryAction = () => fetchCategories(key)
    handleError(error, '获取分类数据失败', retryAction)
    
    // Return mock data as fallback
    return getMockCategories()
  }
}

/**
 * Fetch indices based on selected category
 * @param {string} key - Granularity key/ID
 * @param {string} category_lv_1 - Level 1 category (required)
 * @param {string} category_lv_2 - Level 2 category (optional)
 * @param {string} category_lv_3 - Level 3 category (optional)
 * @returns {Promise<Array>} Array of indicator objects
 */
export const fetchIndicesByCategory = async (key, category_lv_1, category_lv_2 = '', category_lv_3 = '') => {
  try {
    const params = {
      key,
      category_lv_1,
      category_lv_2,
      category_lv_3,
    }
    
    console.log('Fetching indices with params:', params)
    const response = await axios.get('/api/v1/config/data_prep/indices', {
      params,
      headers: {
        token: localStorage.getItem('token'),
      },
    })
    
    console.log('Indices API response:', response.data)
    
    if (response.data.error_code === 0) {
      return response.data.data || []
    } else {
      console.error('Indices API error:', response.data.message)
      // Return mock data for development
      return getMockIndices()
    }
  } catch (error) {
    console.error('Failed to fetch indices:', error)
    
    // Use error handler for consistent error management
    const retryAction = () => fetchIndicesByCategory(key, category_lv_1, category_lv_2, category_lv_3)
    handleError(error, '获取指标数据失败', retryAction)
    
    // Return mock data as fallback
    return getMockIndices()
  }
}

/**
 * Fetch dimensions based on selected category
 * @param {string} key - Granularity key/ID
 * @param {string} category_lv_1 - Level 1 category (required)
 * @param {string} category_lv_2 - Level 2 category (optional)
 * @param {string} category_lv_3 - Level 3 category (optional)
 * @param {Array} indices - Array of selected indicator IDs (optional)
 * @returns {Promise<Array>} Array of dimension objects
 */
export const fetchDimensionsByCategory = async (key, category_lv_1, category_lv_2 = '', category_lv_3 = '', indices = []) => {
  try {
    const params = {
      key,
      category_lv_1,
      category_lv_2,
      category_lv_3,
      indices,
    }
    
    console.log('Fetching dimensions with params:', params)
    const response = await axios.get('/api/v1/config/data_prep/dimensions', {
      params,
      headers: {
        token: localStorage.getItem('token'),
      },
    })
    
    console.log('Dimensions API response:', response.data)
    
    if (response.data.error_code === 0) {
      return response.data.data || []
    } else {
      console.error('Dimensions API error:', response.data.message)
      // Return mock data for development
      return getMockDimensions()
    }
  } catch (error) {
    console.error('Failed to fetch dimensions:', error)
    
    // Use error handler for consistent error management
    const retryAction = () => fetchDimensionsByCategory(key, category_lv_1, category_lv_2, category_lv_3, indices)
    handleError(error, '获取维度数据失败', retryAction)
    
    // Return mock data as fallback
    return getMockDimensions()
  }
}

/**
 * Mock categories data for development/fallback - flat array format
 */
const getMockCategories = () => [
  {
    id: 'cat1',
    name: '总公司',
    parent: null,
    level: 1
  },
  {
    id: 'cat1-1',
    name: '子公司1',
    parent: 'cat1',
    level: 2
  },
  {
    id: 'cat1-2',
    name: '子公司2',
    parent: 'cat1',
    level: 2
  },
  {
    id: 'cat1-1-1',
    name: '部门A',
    parent: 'cat1-1',
    level: 3
  },
  {
    id: 'cat1-1-2',
    name: '部门B',
    parent: 'cat1-1',
    level: 3
  },
  {
    id: 'cat1-2-1',
    name: '部门C',
    parent: 'cat1-2',
    level: 3
  },
  {
    id: 'cat2',
    name: '分公司',
    parent: null,
    level: 1
  },
  {
    id: 'cat2-1',
    name: '华东分公司',
    parent: 'cat2',
    level: 2
  }
]

/**
 * Mock indices data for development/fallback
 */
const getMockIndices = () => [
  {
    id: '101',
    name: 'overdue_amount',
    display_name: '逾期金额',
    category_lv_1: '总公司',
    category_lv_2: '子公司1',
    category_lv_3: '部门A',
    is_permitted: 1,
  },
  {
    id: '102',
    name: 'bill_amount',
    display_name: '账单金额',
    category_lv_1: '总公司',
    category_lv_2: '子公司1',
    category_lv_3: '部门A',
    is_permitted: 1,
  },
]

/**
 * Mock dimensions data for development/fallback
 */
const getMockDimensions = () => [
  {
    id: '201',
    name: 'region',
    display_name: '地区',
    category_lv_1: '总公司',
    category_lv_2: '子公司1',
    category_lv_3: '部门A',
    is_permitted: 1,
  },
  {
    id: '202',
    name: 'branch',
    display_name: '所属分行',
    category_lv_1: '总公司',
    category_lv_2: '子公司1',
    category_lv_3: '部门A',
    is_permitted: 1,
  },
]
