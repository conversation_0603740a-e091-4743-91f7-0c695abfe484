import { useStates } from '@/store/states'

/**
 * Error classification utility
 * Determines whether an error should show system error mask or toast notification
 */

// System error patterns (5xx status codes and critical system failures)
const SYSTEM_ERROR_PATTERNS = {
  // HTTP 5xx status codes
  statusCodes: [500, 501, 502, 503, 504, 505, 506, 507, 508, 509, 510, 511],

  // Network and connectivity issues
  networkErrors: [
    'NETWORK_ERROR',
    'TIMEOUT_ERROR',
    'CONNECTION_REFUSED',
    'DNS_ERROR',
    'CERT_ERROR',
  ],

  // Critical system failures
  systemFailures: [
    'INTERNAL_SERVER_ERROR',
    'SERVICE_UNAVAILABLE',
    'BAD_GATEWAY',
    'GATEWAY_TIMEOUT',
    'DATABASE_ERROR',
    'AUTHENTICATION_SERVICE_DOWN',
  ],
}

// User error patterns (4xx status codes and validation errors)
const USER_ERROR_PATTERNS = {
  // HTTP 4xx status codes (except 408 timeout which is system-level)
  statusCodes: [
    400, 401, 403, 404, 405, 406, 407, 409, 410, 411, 412, 413, 414, 415, 416, 417, 418, 421, 422,
    423, 424, 425, 426, 428, 429, 431, 451,
  ],

  // Validation and user input errors
  validationErrors: [
    'VALIDATION_ERROR',
    'MISSING_REQUIRED_FIELD',
    'INVALID_INPUT',
    'PERMISSION_DENIED',
    'UNAUTHORIZED',
    'FORBIDDEN',
  ],

  // User-specific error messages (Chinese patterns)
  messagePatterns: [
    /请完善以下必填项/,
    /请输入.*/,
    /请选择.*/,
    /格式不正确/,
    /长度不能超过/,
    /不能为空/,
    /用户名或密码错误/,
    /认证失败/,
    /权限不足/,
    /请重新登录/,
  ],
}

// Success notification patterns
const SUCCESS_PATTERNS = {
  messagePatterns: [
    /.*已创建/,
    /.*创建成功/,
    /.*上传成功/,
    /.*保存成功/,
    /.*删除成功/,
    /.*更新成功/,
    /操作成功/,
    /登录成功/,
  ],
}

/**
 * Classify error type based on error object and message
 * @param {Error|Object} error - The error object
 * @param {string} message - Error message
 * @returns {string} - 'system' | 'user' | 'success'
 */
export function classifyError(error, message = '') {
  // Check if it's a success message
  if (message && SUCCESS_PATTERNS.messagePatterns.some((pattern) => pattern.test(message))) {
    return 'success'
  }

  // Check HTTP status codes
  if (error?.response?.status) {
    const status = error.response.status

    // System errors (5xx)
    if (SYSTEM_ERROR_PATTERNS.statusCodes.includes(status)) {
      return 'system'
    }

    // User errors (4xx, except timeout)
    if (USER_ERROR_PATTERNS.statusCodes.includes(status)) {
      return 'user'
    }

    // 408 Request Timeout is considered system error
    if (status === 408) {
      return 'system'
    }
  }

  // Check network errors
  if (error?.request && !error?.response) {
    return 'system'
  }

  // Check error codes/types
  if (error?.code) {
    const errorCode = error.code.toUpperCase()

    if (
      SYSTEM_ERROR_PATTERNS.networkErrors.includes(errorCode) ||
      SYSTEM_ERROR_PATTERNS.systemFailures.includes(errorCode)
    ) {
      return 'system'
    }

    if (USER_ERROR_PATTERNS.validationErrors.includes(errorCode)) {
      return 'user'
    }
  }

  // Check error message patterns
  if (message) {
    // User error patterns
    if (USER_ERROR_PATTERNS.messagePatterns.some((pattern) => pattern.test(message))) {
      return 'user'
    }
  }

  // Default to system error for unknown errors to be safe
  return 'system'
}

/**
 * Handle error display based on classification
 * @param {Error|Object} error - The error object
 * @param {string} message - Error message
 * @param {Function} retryAction - Optional retry function
 * @param {Function} showToast - Toast notification function
 */
export function handleError(error, message = '', retryAction = null, showToast = null) {
  const statesStore = useStates()
  const errorType = classifyError(error, message)

  console.log('Error classification:', {
    error,
    message,
    type: errorType,
    status: error?.response?.status,
    code: error?.code,
  })

  switch (errorType) {
    case 'system':
      // Show system error mask for critical system failures
      const systemTitle = getSystemErrorTitle(error)
      const systemMessage = getSystemErrorMessage(error, message)
      const errorCode = getErrorCode(error)

      statesStore.showSystemError(systemTitle, systemMessage, errorCode, retryAction)
      break

    case 'user':
      // Show toast notification for user errors
      if (showToast) {
        showToast(message || '操作失败，请检查输入', 'error')
      }
      break

    case 'success':
      // Show success toast
      if (showToast) {
        showToast(message, 'success')
      }
      break
  }
}

/**
 * Get appropriate system error title
 */
function getSystemErrorTitle(error) {
  const status = error?.response?.status

  if (status >= 500 && status < 600) {
    switch (status) {
      case 500:
        return '服务器内部错误'
      case 502:
        return '网关错误'
      case 503:
        return '服务暂时不可用'
      case 504:
        return '网关超时'
      default:
        return '服务器错误'
    }
  }

  if (error?.request && !error?.response) {
    return '网络连接错误'
  }

  return '系统错误'
}

/**
 * Get appropriate system error message
 */
function getSystemErrorMessage(error, customMessage) {
  if (
    customMessage &&
    !USER_ERROR_PATTERNS.messagePatterns.some((pattern) => pattern.test(customMessage))
  ) {
    return customMessage
  }

  const status = error?.response?.status

  if (status >= 500 && status < 600) {
    switch (status) {
      case 500:
        return '服务器遇到内部错误，无法完成请求。请稍后重试或联系技术支持。'
      case 502:
        return '网关错误，服务器作为网关或代理时收到无效响应。请稍后重试。'
      case 503:
        return '服务暂时不可用，服务器正在维护或过载。请稍后重试。'
      case 504:
        return '网关超时，服务器作为网关或代理时未及时收到响应。请稍后重试。'
      default:
        return '服务器遇到错误，无法完成请求。请稍后重试。'
    }
  }

  if (error?.request && !error?.response) {
    return '无法连接到服务器，请检查网络连接或稍后重试。'
  }

  return '系统遇到未知错误，请稍后重试或联系技术支持。'
}

/**
 * Extract error code for display
 */
function getErrorCode(error) {
  if (error?.response?.status) {
    return `HTTP ${error.response.status}`
  }

  if (error?.code) {
    return error.code
  }

  return null
}

/**
 * Quick helper to check if error should show system mask
 */
export function isSystemError(error, message = '') {
  return classifyError(error, message) === 'system'
}

/**
 * Quick helper to check if error should show user toast
 */
export function isUserError(error, message = '') {
  return classifyError(error, message) === 'user'
}
