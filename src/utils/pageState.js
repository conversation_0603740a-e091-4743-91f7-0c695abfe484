// 收集页面所有控件的状态
export function collectPageState() {
  const state = {
    // 左侧面板状态
    leftColumn: {
      indicators: Array.from(document.querySelectorAll('.indicator-item')).map((item) => ({
        title: item.querySelector('.indicator-title').textContent,
        value: item.querySelector('.indicator-input').value,
      })),
      dimensions: Array.from(document.querySelectorAll('.dimension-row')).map((row) => ({
        label: row.querySelector('.dimension-label').textContent,
        value: row.querySelector('.dimension-input').value,
      })),
    },
    // 右侧面板状态
    rightColumn: {
      modules: Array.from(document.querySelectorAll('.module-block')).map((module) => ({
        type: module.querySelector('.trend-module') ? 'trend' : 'chart',
        // 可以添加更多模块特定的状态
      })),
    },
    // 数据表格状态
    dataSheet: {
      activeSheet: document.querySelector('.sheet-item.active')?.textContent,
      activeTab: document.querySelector('.tab-btn.active')?.textContent,
      // 表格数据
      sheetData: {
        sheet1: {
          // 保存表头信息，只收集有实际内容的列
          columns: Array.from(document.querySelectorAll('.custom-table th'))
            .filter((th) => th.textContent.trim() && !th.classList.contains('plus-th'))
            .map((th) => ({
              field: th.getAttribute('data-field') || th.textContent.trim(),
              title: th.textContent.trim(),
            })),
          // 保存行数据，只包含实际存在的列
          rows: Array.from(document.querySelectorAll('.custom-table tbody tr')).map((row) => {
            const rowData = {}
            const headers = Array.from(document.querySelectorAll('.custom-table th')).filter(
              (th) => th.textContent.trim() && !th.classList.contains('plus-th'),
            )
            Array.from(row.querySelectorAll('td')).forEach((cell, index) => {
              if (index < headers.length) {
                const header = headers[index]
                const field = header?.getAttribute('data-field') || header?.textContent.trim()
                if (field) {
                  rowData[field] = cell.textContent
                }
              }
            })
            return rowData
          }),
        },
      },
      // 筛选条件
      filters: Array.from(document.querySelectorAll('.filter-input')).reduce(
        (acc, input, index) => {
          const headers = Array.from(document.querySelectorAll('.custom-table th')).filter(
            (th) => th.textContent.trim() && !th.classList.contains('plus-th'),
          )
          if (index < headers.length) {
            const header = headers[index]
            const field = header?.getAttribute('data-field') || header?.textContent.trim()
            if (field) {
              acc[field] = input.value
            }
          }
          return acc
        },
        {},
      ),
      // 排序状态
      sortField:
        document.querySelector('.custom-table th:not(.plus-th)')?.getAttribute('data-field') ||
        document.querySelector('.custom-table th:not(.plus-th)')?.textContent.trim(),
      sortOrder:
        document.querySelector('.custom-table th:not(.plus-th) span')?.textContent === '▲'
          ? 'asc'
          : 'desc',
      // 聚合计算状态
      activeAgg: document.querySelector('.agg-btn.active')?.textContent,
      // 可视化状态
      visX: document.querySelector('.vis-select')?.value,
      visY: Array.from(document.querySelectorAll('.vis-select'))
        .slice(1)
        .map((select) => select.value),
    },
  }
  return state
}

// 将状态应用到页面控件
export function applyPageState(state, sheetData, sheetColumns) {
  // 应用左侧面板状态
  if (state.leftColumn) {
    // 应用指标状态
    state.leftColumn.indicators?.forEach((indicator, index) => {
      const items = document.querySelectorAll('.indicator-item')
      if (items[index]) {
        const titleEl = items[index].querySelector('.indicator-title')
        const inputEl = items[index].querySelector('.indicator-input')
        if (titleEl) titleEl.textContent = indicator.title
        if (inputEl) inputEl.value = indicator.value
      }
    })

    // 应用维度状态
    state.leftColumn.dimensions?.forEach((dimension, index) => {
      const rows = document.querySelectorAll('.dimension-row')
      if (rows[index]) {
        const labelEl = rows[index].querySelector('.dimension-label')
        const inputEl = rows[index].querySelector('.dimension-input')
        if (labelEl) labelEl.textContent = dimension.label
        if (inputEl) inputEl.value = dimension.value
      }
    })
  }

  // 应用右侧面板状态
  if (state.rightColumn) {
    // 这里需要根据实际模块类型进行更复杂的处理
    // 目前只是示例
    state.rightColumn.modules?.forEach((module, index) => {
      const modules = document.querySelectorAll('.module-block')
      if (modules[index]) {
        // 根据module.type进行相应的处理
      }
    })
  }

  // 应用数据表格状态
  if (state.dataSheet) {
    // 设置活动sheet
    // if (state.dataSheet.activeSheet) {
    //   const sheetBtn = Array.from(document.querySelectorAll('.sheet-item'))
    //     .find(btn => btn.textContent === state.dataSheet.activeSheet)
    //   if (sheetBtn) sheetBtn.click()
    // }

    // // 设置活动tab
    // if (state.dataSheet.activeTab) {
    //   const tabBtn = Array.from(document.querySelectorAll('.tab-btn'))
    //     .find(btn => btn.textContent === state.dataSheet.activeTab)
    //   if (tabBtn) tabBtn.click()
    // }

    // 应用表格数据
    if (state.dataSheet.sheetData?.sheet1) {
      const { columns, rows } = state.dataSheet.sheetData.sheet1

      // 更新 sheetData
      if (sheetData) {
        // 直接赋值，不使用任何合并操作
        sheetData.value = {
          sheet1: rows.map((row) => ({ ...row })), // 创建新的对象，避免引用问题
          sheet2: [],
          sheet3: [],
        }
      }

      // 更新 sheetColumns
      if (sheetColumns) {
        // 直接赋值，不使用任何合并操作
        sheetColumns.value = {
          sheet1: columns.map((col) => ({ ...col })), // 创建新的对象，避免引用问题
          sheet2: [],
          sheet3: [],
        }
      }
    }

    // 应用筛选条件
    if (state.dataSheet.filters) {
      Object.entries(state.dataSheet.filters).forEach(([field, value]) => {
        const headerIndex = Array.from(document.querySelectorAll('.custom-table th')).findIndex(
          (th) => (th.getAttribute('data-field') || th.textContent.trim()) === field,
        )
        if (headerIndex !== -1) {
          const filterInput = document.querySelectorAll('.filter-input')[headerIndex]
          if (filterInput) filterInput.value = value
        }
      })
    }

    // 应用排序状态
    if (state.dataSheet.sortField) {
      const header = Array.from(document.querySelectorAll('.custom-table th')).find(
        (th) =>
          (th.getAttribute('data-field') || th.textContent.trim()) === state.dataSheet.sortField,
      )
      if (header) {
        // 移除点击事件，直接设置排序状态
        const sortOrder = state.dataSheet.sortOrder || 'asc'
        const sortSpan = header.querySelector('span')
        if (sortSpan) {
          sortSpan.textContent = sortOrder === 'asc' ? '▲' : '▼'
        }
      }
    }

    // 应用聚合计算状态
    // if (state.dataSheet.activeAgg) {
    //   const aggBtn = Array.from(document.querySelectorAll('.agg-btn'))
    //     .find(btn => btn.textContent === state.dataSheet.activeAgg)
    //   if (aggBtn) aggBtn.click()
    // }

    // 应用可视化状态
    // if (state.dataSheet.visX) {
    //   const visXSelect = document.querySelector('.vis-select')
    //   if (visXSelect) visXSelect.value = state.dataSheet.visX
    // }
    // if (state.dataSheet.visY) {
    //   const visYSelects = Array.from(document.querySelectorAll('.vis-select')).slice(1)
    //   state.dataSheet.visY.forEach((value, index) => {
    //     if (visYSelects[index]) visYSelects[index].value = value
    //   })
    // }
  }
}
