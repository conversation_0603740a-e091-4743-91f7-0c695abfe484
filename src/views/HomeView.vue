<template>
  <div class="home-container">
    <div class="welcome-section">
      <h1>欢迎使用智能统一归因平台</h1>
      <p class="subtitle">选择您想要使用的功能模块</p>
    </div>

    <div class="modules-grid">
      <div class="module-card" @click="navigateTo('/inductive')">
        <div class="module-icon">🔍</div>
        <h2>引导式归因</h2>
        <p>使用引导式归因，探索数据趋势和模式</p>
        <button class="enter-button">进入分析</button>
      </div>

      <div class="module-card" @click="navigateTo('/chat')">
        <div class="module-icon">💬</div>
        <h2>智能对话</h2>
        <p>通过自然语言交互，获取数据洞察和建议</p>
        <button class="enter-button">开始对话</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useStates } from '@/store/states'

const router = useRouter()
const statesStore = useStates()

const navigateTo = (path) => {
  router.push(path)
}

onMounted(() => {
  // TODO: demo标题
  statesStore.$patch({ pageName: 'home', title: '智能统一归因平台' })
})
</script>

<style scoped>
.home-container {
  /* min-height: 80vh; */
  padding: 40px 20px;
  background: #f7faff;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.welcome-section {
  text-align: center;
  margin-bottom: 60px;
}

.welcome-section h1 {
  font-size: 2.5rem;
  color: #2c3e50;
  margin-bottom: 16px;
}

.subtitle {
  font-size: 1.2rem;
  color: #666;
}

.modules-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  max-width: 1200px;
  width: 100%;
  padding: 0 20px;
}

.module-card {
  background: white;
  border-radius: 12px;
  padding: 30px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.module-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.module-icon {
  font-size: 3rem;
  margin-bottom: 10px;
}

.module-card h2 {
  color: #2c3e50;
  font-size: 1.5rem;
  margin: 0;
}

.module-card p {
  color: #666;
  line-height: 1.6;
  margin: 0;
}

.enter-button {
  margin-top: auto;
  padding: 12px 24px;
  background: #42b983;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.enter-button:hover {
  background: #3aa876;
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(66, 185, 131, 0.2);
}

@media (max-width: 768px) {
  .modules-grid {
    grid-template-columns: 1fr;
    max-width: 400px;
  }

  .welcome-section h1 {
    font-size: 2rem;
  }

  .module-card {
    padding: 20px;
  }
}
</style>
