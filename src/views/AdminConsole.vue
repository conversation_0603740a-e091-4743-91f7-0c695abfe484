<template>
  <div class="admin-console-outer">
    <div class="admin-console-layout">
      <UserNavBar />
      <div class="admin-console-main">
        <template v-if="userRole === '系统管理员'">
          <h1 class="admin-title">管理台</h1>
          <div class="admin-sections">
            <!-- 数据权限管理入口 -->
            <section class="admin-card admin-card-action" @click="openDataAuth">
              <h2>数据权限管理</h2>
              <p>点击进入部门-数据表分配管理</p>
            </section>
            <!-- 关联关系管理入口 -->
            <section class="admin-card admin-card-action" @click="openRelationManager">
              <h2>关联关系管理</h2>
              <p>设置指标与维度的关联关系，以及粒度与指标/维度的关联</p>
            </section>
            <!-- 用户权限管理 -->
            <section class="admin-card">
              <h2>用户权限管理</h2>
              <p>（此处为用户权限管理功能区，后续可扩展为用户列表、角色分配、权限编辑等操作）</p>
            </section>
            <!-- 任务管理 -->
            <section class="admin-card">
              <h2>任务管理</h2>
              <p>
                （此处为任务管理功能区，包括任务管理及跑批管理，后续可扩展为任务列表、批量操作等）
              </p>
            </section>
          </div>
          <!-- 数据权限管理窗口 -->
          <DataAuthWindow v-if="showDataAuth" @close="closeDataAuth" />
          <!-- 关联关系管理窗口 -->
          <RelationManagerWindow v-if="showRelationManager" @close="closeRelationManager" />
        </template>
        <template v-else>
          <div class="admin-warning">
            <svg width="48" height="48" viewBox="0 0 48 48" fill="none">
              <circle cx="24" cy="24" r="22" stroke="#e53935" stroke-width="4" fill="#fff0f0" />
              <path d="M24 14v12" stroke="#e53935" stroke-width="4" stroke-linecap="round" />
              <circle cx="24" cy="32" r="2.5" fill="#e53935" />
            </svg>
            <div class="warning-text">无权限访问：仅管理员可进入管理台</div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup>
import UserNavBar from '../components/common/UserNavBar.vue'
import { useStates } from '@/store/states'
import { computed, onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import DataAuthWindow from '../components/DataAuthWindow.vue'
import RelationManagerWindow from '../components/RelationManagerWindow.vue'

const statesStore = useStates()
const userRole = computed(() => statesStore.userRole)
const router = useRouter()

onMounted(() => {
  // Set page name and title for header
  statesStore.$patch({ pageName: '管理台', title: '管理台' })
})

// 数据权限管理窗口状态
const showDataAuth = ref(false)
function openDataAuth() {
  showDataAuth.value = true
}
function closeDataAuth() {
  showDataAuth.value = false
}
// 关联关系管理窗口状态
const showRelationManager = ref(false)
function openRelationManager() {
  showRelationManager.value = true
}
function closeRelationManager() {
  showRelationManager.value = false
}
</script>

<style scoped>
.admin-console-outer {
  width: 100vw;
  min-height: 80vh;
  background: #f5f8fc;
  display: flex;
  justify-content: center;
}
.admin-console-layout {
  display: flex;
  width: 100%;
  max-width: 1400px;
  min-height: 80vh;
  background: transparent;
}
.admin-console-main {
  flex: 1;
  padding: 48px 64px;
  display: flex;
  flex-direction: column;
  gap: 32px;
  min-width: 0;
  box-sizing: border-box;
  align-items: center;
  margin-left: 220px;
}
.admin-title {
  font-size: 2.2rem;
  color: #1976d2;
  font-weight: bold;
  margin-bottom: 24px;
  letter-spacing: 2px;
}
.admin-sections {
  display: flex;
  flex-wrap: wrap;
  gap: 32px;
  width: 100%;
  justify-content: center;
}
.admin-card {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 24px 0 rgba(0, 32, 72, 0.08);
  padding: 32px 40px;
  min-width: 320px;
  flex: 1 1 350px;
  max-width: 420px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 18px;
}
.admin-card h2 {
  color: #1976d2;
  font-size: 1.3rem;
  margin-bottom: 8px;
}
.admin-card p {
  color: #000;
  font-size: 1rem;
}
.admin-warning {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #fff0f0;
  border: 1.5px solid #e53935;
  border-radius: 16px;
  padding: 48px 32px;
  margin-top: 80px;
  box-shadow: 0 4px 24px 0 rgba(229, 57, 53, 0.08);
}
.warning-text {
  color: #e53935;
  font-size: 1.2rem;
  font-weight: 500;
  margin-top: 18px;
}
.admin-card-action {
  cursor: pointer;
  transition:
    box-shadow 0.2s,
    background 0.2s;
  border: 2px solid transparent;
}
.admin-card-action:hover {
  box-shadow: 0 6px 32px 0 rgba(25, 118, 210, 0.13);
  background: #f5f8ff;
  border: 2px solid #1976d2;
}
@media (max-width: 1100px) {
  .admin-console-main {
    padding: 32px 8px;
    margin-left: 220px;
  }
  .admin-card {
    padding: 24px 8px;
    min-width: 220px;
    max-width: 98vw;
  }
  .admin-sections {
    gap: 16px;
  }
}
@media (max-width: 700px) {
  .admin-console-layout {
    flex-direction: column;
    align-items: stretch;
  }
  .admin-console-main {
    padding: 8px 2px;
    gap: 16px;
    margin-left: 0;
  }
  .admin-card {
    padding: 16px 2px;
    max-width: 98vw;
  }
  .admin-sections {
    flex-direction: column;
    gap: 12px;
  }
}
</style>
