<template>
  <div class="chat-isd-container">
    <!-- Sidebar Drawer Toggle But<PERSON> (visible on small screens) -->
    <div class="sidebar-toggle-btn-wrapper" v-if="!sidebarOpen && isMobile">
      <button class="sidebar-toggle-btn" @click="sidebarOpen = !sidebarOpen">☰</button>
    </div>

    <!-- Sidebar (Drawer) -->
    <aside class="sidebar" :class="{ 'sidebar-hidden': !sidebarOpen }">
      <button class="new-chat-btn">+ 新建对话</button>
      <ul class="chat-list">
        <li class="chat-item" v-for="(item, idx) in 6" :key="idx">
          <span class="chat-icon">📊</span>
          <div class="chat-info">
            <div class="chat-title">对话标题示例</div>
            <div class="chat-time">2025-06-11 12:34:01</div>
          </div>
        </li>
      </ul>
    </aside>

    <!-- Overlay for sidebar on mobile -->
    <div class="sidebar-overlay" v-if="sidebarOpen && isMobile" @click="sidebarOpen = false"></div>

    <!-- Main Content -->
    <main class="main-content">
      <!-- Business Topic/Subtopic Selection (with greeting) -->
      <section class="topic-selector" v-if="subTopic == ''">
        <div class="greeting-title">您好，我是智能研判分析助手</div>
        <div class="greeting-subtitle">
          我能帮助您更好地分析业务经营情况、诊断业务问题、助力对公业务的科学决策！
        </div>
        <div class="topic-selector-header">
          <span class="topic-header">业务主题</span>
          <span class="subtopic-header">业务子主题</span>
        </div>
        <div class="topic-selector-body">
          <div class="topic-list">
            <div
              v-for="item in availableTopics"
              :key="item.topic"
              :class="['topic-item', { selected: topic === item.topic }]"
              @click="() => handleClickTopic(item.topic)"
            >
              <span>{{ item.topic }}</span>
              <span v-if="topic === item.topic" class="arrow">&gt;</span>
            </div>
          </div>
          <div class="subtopic-list">
            <div
              v-for="sub in availableTopics.find((t) => t.topic === topic)?.subTopics"
              :key="sub"
              :class="['subtopic-item', { selected: subTopic === sub }]"
              @click="subTopic = sub"
            >
              <span>{{ sub }}</span>
            </div>
          </div>
        </div>
      </section>
      <!-- Welcome and FAQ -->
      <section class="welcome-section" v-else>
        <h2>欢迎进入{{ subTopic }}专题！</h2>
        <div class="faq-box">
          <div class="faq-title">常见问题</div>
          <ul class="faq-list">
            <li>上月末对公财富规模如何</li>
            <li>近三年对公财富年日均余额变动趋势情况</li>
            <li>为什么近期财富年日均余额出现大幅下降</li>
          </ul>
        </div>
      </section>

      <!-- Chat Area -->
      <section class="chat-area" v-if="subTopic.length > 0">
        <div class="model-select">
          <span class="model-label">模型选择：</span>
          <span class="model-value">QWQ</span>
        </div>
        <div class="chat-input-row">
          <input class="chat-input" type="text" placeholder="请输入您的问题..." />
          <span class="attach-icon">📎</span>
          <button class="send-btn">➤</button>
        </div>
      </section>
    </main>
  </div>
</template>

<script setup>
import { storeToRefs } from 'pinia'
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { useStates } from '@/store/states'

const statesStore = useStates()
const { pageName } = storeToRefs(statesStore)

const availableTopics = [
  {
    topic: '对公存款',
    subTopics: [''],
  },
  {
    topic: '对公财富',
    subTopics: ['对公财富', '对公理财', '对公代销', '信银理财'],
  },
  {
    topic: '对公贷款',
    subTopics: [''],
  },
  {
    topic: '对公客户',
    subTopics: [''],
  },
  {
    topic: '对公效益',
    subTopics: [''],
  },
]

const topic = ref('')
const subTopic = ref('')

const sidebarOpen = ref(false)
const isMobile = ref(false)

function handleResize() {
  isMobile.value = window.innerWidth <= 900
  if (!isMobile.value) sidebarOpen.value = true
}

function handleClickTopic(tpc) {
  topic.value = tpc
  subTopic.value = ''
}

onMounted(() => {
  handleResize()
  window.addEventListener('resize', handleResize)
  statesStore.$patch({ pageName: '智能研判对话', title: '智能研判对话' })
})
onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.chat-isd-container {
  display: flex;
  height: calc(100vh - 120px);
  background: #f3f6fb;
  position: relative;
  color: #000;
}

/* Sidebar styles */
.sidebar {
  width: 320px;
  background: #e9eef6;
  padding: 24px 0 0 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: transform 0.3s;
  z-index: 20;
}
.sidebar-hidden {
  transform: translateX(-100%);
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
}

/* Toggle button wrapper for mobile */
.sidebar-toggle-btn-wrapper {
  display: none;
}

.sidebar-toggle-btn {
  display: none;
  background: #fff;
  border: none;
  border-radius: 6px;
  font-size: 28px;
  width: 44px;
  height: 44px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  cursor: pointer;
  margin: 18px 0 0 18px;
  /* Remove absolute/positioning */
  position: static;
  z-index: auto;
}

/* Overlay for mobile drawer */
.sidebar-overlay {
  display: none;
}

@media (max-width: 900px) {
  .chat-isd-container {
    flex-direction: column;
  }
  .sidebar {
    position: fixed;
    left: 0;
    top: 0;
    height: 100%;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.08);
    background: #e9eef6;
    z-index: 20;
    transition: transform 0.3s;
  }
  .sidebar-hidden {
    transform: translateX(-100%);
  }
  .sidebar-toggle-btn-wrapper {
    display: block;
    width: 100%;
    /* Ensure it doesn't overlay, but stays at the top */
    height: 60px;
    background: transparent;
  }
  .sidebar-toggle-btn {
    display: block;
    position: static;
    margin: 18px 0 0 18px;
    z-index: auto;
  }
  .main-content {
    padding: 24px 8px 0 8px;
  }
  .sidebar-overlay {
    display: block;
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.18);
    z-index: 10;
  }
}

.new-chat-btn {
  padding: 8px 16px;
  background: #1976d2;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}
.new-chat-btn:hover:not(:disabled) {
  background: #1565c0;
}
.anew-chat-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

/* Main content styles */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 48px 48px 0 48px;
  min-width: 0;
}
.welcome-section {
  background: #fff;
  border-radius: 12px;
  padding: 32px;
  margin-bottom: 32px;
}
.faq-box {
  border: 1px dashed #b3c0d1;
  border-radius: 8px;
  padding: 24px;
  margin-top: 16px;
}
.faq-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 12px;
  color: #000;
}
.faq-list {
  list-style: disc inside;
  color: #000;
  font-size: 16px;
}
.chat-area {
  margin-top: auto;
  background: #e9eef6;
  border-radius: 12px;
  padding: 24px;
  display: flex;
  flex-direction: column;
}
.model-select {
  margin-bottom: 12px;
  font-size: 16px;
}
.model-label {
  font-weight: bold;
  color: #000;
}
.model-value {
  background: #dbe4ee;
  border-radius: 6px;
  padding: 2px 8px;
  margin-left: 8px;
  color: #000;
}
.chat-input-row {
  display: flex;
  align-items: center;
}
.chat-input {
  flex: 1;
  height: 40px;
  border-radius: 8px;
  border: 1px solid #b3c0d1;
  padding: 0 12px;
  font-size: 16px;
  margin-right: 12px;
}
.attach-icon {
  font-size: 22px;
  margin-right: 12px;
  cursor: pointer;
}
.send-btn {
  width: 40px;
  height: 40px;
  background: #4a90e2;
  color: #fff;
  border: none;
  border-radius: 50%;
  font-size: 22px;
  cursor: pointer;
}

/* Responsive adjustments */
@media (max-width: 600px) {
  .main-content {
    padding: 12px 2px 0 2px;
  }
  .welcome-section,
  .chat-area {
    padding: 12px;
  }
  .faq-box {
    padding: 10px;
  }
}

.topic-selector,
.welcome-section {
  background: #fff;
  border-radius: 12px;
  padding: 32px 32px 24px 32px;
  margin-bottom: 32px;
  width: 100%;
  box-sizing: border-box;
}
.greeting-title {
  font-size: 32px;
  font-weight: bold;
  color: #000;
  margin-bottom: 8px;
}
.greeting-subtitle {
  font-size: 18px;
  color: #666;
  margin-bottom: 24px;
}
.topic-selector-header {
  display: flex;
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 18px;
}
.topic-header,
.subtopic-header {
  flex: 1;
  border-bottom: 2px solid #1976d2;
  padding-bottom: 4px;
  color: #000;
}
.topic-header {
  margin-right: 32px;
}
.topic-selector-body {
  display: flex;
  gap: 32px;
}
.topic-list,
.subtopic-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.topic-item,
.subtopic-item {
  font-size: 18px;
  color: #000;
  cursor: pointer;
  padding: 2px 0;
  display: flex;
  align-items: center;
  font-weight: normal;
  border-bottom: 1px solid #e0e0e0;
}
.topic-item.selected,
.subtopic-item.selected {
  font-weight: bold;
  color: #000;
  background: none;
}
.topic-item .arrow {
  margin-left: 8px;
  font-size: 18px;
  color: #000;
  font-weight: bold;
}
@media (max-width: 600px) {
  .topic-selector,
  .welcome-section {
    padding: 12px 4px 8px 4px;
    margin-bottom: 12px;
  }
  .greeting-title {
    font-size: 22px;
  }
  .greeting-subtitle {
    font-size: 14px;
  }
  .topic-selector-header {
    font-size: 16px;
  }
  .topic-list,
  .subtopic-list {
    font-size: 16px;
  }
}
</style>
