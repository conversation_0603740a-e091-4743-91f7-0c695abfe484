<template>
  <div class="task-view-outer">
    <div class="task-view-layout">
      <UserNavBar />
      <div class="task-view-main">
        <div class="task-header">
          <h2 class="task-title">归因任务管理</h2>
          <div class="task-search-bar">
            <span class="search-icon">
              <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                <circle cx="11" cy="11" r="7" stroke="#1976d2" stroke-width="2" />
                <path
                  d="M20 20l-3.5-3.5"
                  stroke="#1976d2"
                  stroke-width="2"
                  stroke-linecap="round"
                />
              </svg>
            </span>
            <input v-model="searchQuery" class="search-input" placeholder="搜索任务名称/类型..." />
          </div>
        </div>
        <div class="task-table-wrapper">
          <table class="task-table">
            <thead>
              <tr>
                <th style="width: 40px"></th>
                <th>ID</th>
                <th>任务名称</th>
                <th>任务类型</th>
                <th>创建日期</th>
                <th>更新日期</th>
                <th>任务状态</th>
                <th>跑批配置</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <template v-for="task in filteredTasks" :key="task.id">
                <tr class="task-row">
                  <td>
                    <button
                      class="expand-btn"
                      @click="toggleTaskExpansion(task.id)"
                      :class="{ expanded: expandedTasks.includes(task.id) }"
                      title="展开实例列表"
                    >
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                        <path
                          d="M6 9l6 6 6-6"
                          stroke="#666"
                          stroke-width="2"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                      </svg>
                    </button>
                  </td>
                  <td>{{ task.id }}</td>
                  <td>{{ task.name }}</td>
                  <td>{{ task.type }}</td>
                  <td>{{ formatDate(task.createdAt) }}</td>
                  <td>{{ formatDate(task.updatedAt) }}</td>
                  <td>
                    <span
                      class="status-indicator"
                      :class="task.status"
                      :title="statusText(task.status)"
                    >
                      <span class="status-dot">
                        <svg
                          v-if="task.status === 'completed'"
                          width="14"
                          height="14"
                          viewBox="0 0 24 24"
                          fill="none"
                        >
                          <circle cx="12" cy="12" r="10" :fill="statusColor(task.status)" />
                          <path
                            d="M8 12l3 3 5-5"
                            stroke="#fff"
                            stroke-width="2"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </svg>
                        <svg
                          v-else-if="task.status === 'running'"
                          width="14"
                          height="14"
                          viewBox="0 0 24 24"
                          fill="none"
                        >
                          <circle cx="12" cy="12" r="10" :fill="statusColor(task.status)" />
                          <path
                            d="M12 7v5l3 3"
                            stroke="#fff"
                            stroke-width="2"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </svg>
                        <svg
                          v-else-if="task.status === 'failed'"
                          width="14"
                          height="14"
                          viewBox="0 0 24 24"
                          fill="none"
                        >
                          <circle cx="12" cy="12" r="10" :fill="statusColor(task.status)" />
                          <path
                            d="M9 9l6 6M15 9l-6 6"
                            stroke="#fff"
                            stroke-width="2"
                            stroke-linecap="round"
                          />
                        </svg>
                        <svg v-else width="14" height="14" viewBox="0 0 24 24" fill="none">
                          <circle cx="12" cy="12" r="10" fill="#bbb" />
                        </svg>
                      </span>
                      <span class="status-label">{{ statusText(task.status) }}</span>
                    </span>
                  </td>
                  <td>
                    <button class="config-btn" @click="showConfig(task)" title="查看配置">
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                        <circle cx="12" cy="12" r="3" stroke="#1976d2" stroke-width="2" />
                        <path
                          d="M12 1v6M12 17v6M4.22 4.22l4.24 4.24M15.54 15.54l4.24 4.24M1 12h6M17 12h6M4.22 19.78l4.24-4.24M15.54 8.46l4.24-4.24"
                          stroke="#1976d2"
                          stroke-width="2"
                          stroke-linecap="round"
                        />
                      </svg>
                      配置
                    </button>
                  </td>
                  <td>
                    <div class="action-circles">
                      <button
                        class="circle-btn details-btn"
                        title="详情"
                        @click="showTaskDetails(task)"
                      >
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                          <circle cx="12" cy="12" r="10" stroke="#1976d2" stroke-width="2" />
                          <path
                            d="M12 16v-4M12 8h.01"
                            stroke="#1976d2"
                            stroke-width="2"
                            stroke-linecap="round"
                          />
                        </svg>
                      </button>
                      <button
                        class="circle-btn online-btn"
                        :title="task.online ? '下线' : '上线'"
                        @click="toggleOnline(task)"
                        :style="{
                          background: task.online ? '#e8f5e8' : '#fff3e0',
                          color: task.online ? '#2e7d32' : '#f57c00',
                        }"
                      >
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                          <circle
                            cx="12"
                            cy="12"
                            r="10"
                            :stroke="task.online ? '#2e7d32' : '#f57c00'"
                            stroke-width="2"
                            fill="none"
                          />
                          <path
                            d="M12 7v6"
                            :stroke="task.online ? '#2e7d32' : '#f57c00'"
                            stroke-width="2"
                            stroke-linecap="round"
                          />
                          <path
                            d="M7 14a5 5 0 0 0 10 0"
                            :stroke="task.online ? '#2e7d32' : '#f57c00'"
                            stroke-width="2"
                            stroke-linecap="round"
                            fill="none"
                          />
                        </svg>
                      </button>
                      <button
                        class="circle-btn execute-btn"
                        title="执行"
                        @click="executeTask(task)"
                      >
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                          <path d="M8 5v14l11-7z" fill="#42b983" />
                        </svg>
                      </button>
                      <button
                        class="circle-btn delete-btn"
                        title="删除"
                        @click="deleteTask(task.id)"
                      >
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                          <rect
                            x="5"
                            y="7"
                            width="14"
                            height="12"
                            rx="2"
                            stroke="#e53935"
                            stroke-width="2"
                          />
                          <path
                            d="M9 11v4M15 11v4"
                            stroke="#e53935"
                            stroke-width="2"
                            stroke-linecap="round"
                          />
                          <path d="M10 7V5a2 2 0 0 1 4 0v2" stroke="#e53935" stroke-width="2" />
                          <rect x="8" y="11" width="2" height="4" fill="#e53935" />
                          <rect x="14" y="11" width="2" height="4" fill="#e53935" />
                        </svg>
                      </button>
                    </div>
                  </td>
                </tr>
                <!-- Instance List Row -->
                <tr v-if="expandedTasks.includes(task.id)" class="instance-row">
                  <td colspan="9" class="instance-cell">
                    <div class="instance-container">
                      <h4 class="instance-title">运行记录</h4>
                      <table class="instance-table">
                        <thead>
                          <tr>
                            <th>ID</th>
                            <th>状态</th>
                            <th>开始时间</th>
                            <th>结束时间</th>
                            <th>操作</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr v-for="instance in getTaskInstances(task.id)" :key="instance.id">
                            <td>{{ instance.id }}</td>
                            <td>
                              <span class="instance-status" :class="instance.status">
                                {{ instanceStatusText(instance.status) }}
                              </span>
                            </td>
                            <td>{{ formatDate(instance.startTime) }}</td>
                            <td>{{ formatDate(instance.endTime) }}</td>
                            <td>
                              <div class="instance-actions">
                                <button
                                  class="instance-btn view-btn"
                                  title="查看"
                                  @click="viewInstance(instance.id)"
                                >
                                  <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                                    <path
                                      d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"
                                      stroke="#1976d2"
                                      stroke-width="2"
                                    />
                                    <circle
                                      cx="12"
                                      cy="12"
                                      r="3"
                                      stroke="#1976d2"
                                      stroke-width="2"
                                    />
                                  </svg>
                                </button>
                                <button
                                  class="instance-btn rerun-btn"
                                  title="重跑"
                                  @click="rerunInstance(instance.id)"
                                >
                                  <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                                    <path
                                      d="M12 5V2M12 2l-2 2M12 2l2 2"
                                      stroke="#ff9800"
                                      stroke-width="2"
                                      stroke-linecap="round"
                                      stroke-linejoin="round"
                                    />
                                    <path
                                      d="M12 22a10 10 0 1 1 7.07-2.93"
                                      stroke="#ff9800"
                                      stroke-width="2"
                                      stroke-linecap="round"
                                      fill="none"
                                    />
                                    <path
                                      d="M20 16v5h-5"
                                      stroke="#ff9800"
                                      stroke-width="2"
                                      stroke-linecap="round"
                                      stroke-linejoin="round"
                                    />
                                  </svg>
                                </button>
                                <button
                                  class="instance-btn pause-btn"
                                  title="暂停"
                                  @click="pauseInstance(instance.id)"
                                >
                                  <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                                    <rect
                                      x="6"
                                      y="4"
                                      width="3"
                                      height="16"
                                      rx="1.5"
                                      fill="#1976d2"
                                    />
                                    <rect
                                      x="15"
                                      y="4"
                                      width="3"
                                      height="16"
                                      rx="1.5"
                                      fill="#1976d2"
                                    />
                                  </svg>
                                </button>
                                <button
                                  class="instance-btn delete-btn"
                                  title="删除"
                                  @click="deleteInstance(instance.id)"
                                >
                                  <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                                    <rect
                                      x="5"
                                      y="7"
                                      width="14"
                                      height="12"
                                      rx="2"
                                      stroke="#e53935"
                                      stroke-width="2"
                                    />
                                    <path
                                      d="M9 11v4M15 11v4"
                                      stroke="#e53935"
                                      stroke-width="2"
                                      stroke-linecap="round"
                                    />
                                    <path
                                      d="M10 7V5a2 2 0 0 1 4 0v2"
                                      stroke="#e53935"
                                      stroke-width="2"
                                    />
                                    <rect x="8" y="11" width="2" height="4" fill="#e53935" />
                                    <rect x="14" y="11" width="2" height="4" fill="#e53935" />
                                  </svg>
                                </button>
                              </div>
                            </td>
                          </tr>
                          <tr v-if="getTaskInstances(task.id).length === 0">
                            <td colspan="5" class="empty-instance">暂无实例</td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </td>
                </tr>
              </template>
              <tr v-if="filteredTasks.length === 0">
                <td colspan="9" class="empty-row">暂无任务</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Configuration Popup -->
    <div v-if="showConfigPopup" class="config-popup-overlay" @click="closeConfig">
      <div class="config-popup" @click.stop>
        <div class="config-popup-header">
          <h3>跑批配置 - {{ selectedTask?.name }}</h3>
          <button class="close-btn" @click="closeConfig">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
              <path
                d="M18 6L6 18M6 6l12 12"
                stroke="#666"
                stroke-width="2"
                stroke-linecap="round"
              />
            </svg>
          </button>
        </div>
        <div class="config-popup-content" v-if="selectedTask">
          <div class="config-section">
            <h4>基础配置</h4>
            <div class="config-item">
              <span class="config-label">任务ID:</span>
              <span class="config-value">{{ selectedTask.id }}</span>
            </div>
            <div class="config-item">
              <span class="config-label">任务名称:</span>
              <span class="config-value">{{ selectedTask.name }}</span>
            </div>
            <div class="config-item">
              <span class="config-label">任务类型:</span>
              <span class="config-value">{{ selectedTask.type }}</span>
            </div>
          </div>
          <div class="config-section">
            <h4>执行配置</h4>
            <div class="config-item">
              <span class="config-label">数据源:</span>
              <span class="config-value">{{
                selectedTask.config?.dataSource || '默认数据源'
              }}</span>
            </div>
            <div class="config-item">
              <span class="config-label">执行频率:</span>
              <span class="config-value">{{ selectedTask.config?.frequency || '每日' }}</span>
            </div>
            <div class="config-item">
              <span class="config-label">超时时间:</span>
              <span class="config-value">{{ selectedTask.config?.timeout || '30分钟' }}</span>
            </div>
            <div class="config-item">
              <span class="config-label">并发数:</span>
              <span class="config-value">{{ selectedTask.config?.concurrency || '5' }}</span>
            </div>
          </div>
          <div class="config-section">
            <h4>高级配置</h4>
            <div class="config-item">
              <span class="config-label">重试次数:</span>
              <span class="config-value">{{ selectedTask.config?.retryCount || '3' }}</span>
            </div>
            <div class="config-item">
              <span class="config-label">失败处理:</span>
              <span class="config-value">{{ selectedTask.config?.failureAction || '停止' }}</span>
            </div>
            <div class="config-item">
              <span class="config-label">日志级别:</span>
              <span class="config-value">{{ selectedTask.config?.logLevel || 'INFO' }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import UserNavBar from '../components/common/UserNavBar.vue'
import { useRouter } from 'vue-router'

const router = useRouter()
// TODO: Replace with backend API call
const tasks = ref([
  {
    id: 1,
    name: '个贷催收场景分析',
    type: '引导式',
    createdAt: '2024-01-15T10:30:00',
    updatedAt: '2024-01-16T14:20:00',
    status: 'completed',
    config: {
      dataSource: '个贷数据库',
      frequency: '每日',
      timeout: '30分钟',
      concurrency: '5',
      retryCount: '3',
      failureAction: '停止',
      logLevel: 'INFO',
    },
  },
  {
    id: 2,
    name: '信用卡逾期归因分析',
    type: '引导式',
    createdAt: '2024-01-15T14:20:00',
    updatedAt: '2024-01-16T16:45:00',
    status: 'running',
    config: {
      dataSource: '信用卡数据库',
      frequency: '每小时',
      timeout: '45分钟',
      concurrency: '3',
      retryCount: '5',
      failureAction: '继续',
      logLevel: 'DEBUG',
    },
  },
  {
    id: 3,
    name: '小微企业风险评估',
    type: '对话式',
    createdAt: '2024-01-14T16:45:00',
    updatedAt: '2024-01-15T09:30:00',
    status: 'failed',
    config: {
      dataSource: '企业数据库',
      frequency: '每周',
      timeout: '60分钟',
      concurrency: '2',
      retryCount: '2',
      failureAction: '停止',
      logLevel: 'WARN',
    },
  },
  {
    id: 4,
    name: '消费金融场景分析',
    type: '引导式',
    createdAt: '2024-01-14T09:15:00',
    updatedAt: '2024-01-15T11:20:00',
    status: 'completed',
    config: {
      dataSource: '消费金融数据库',
      frequency: '每日',
      timeout: '20分钟',
      concurrency: '8',
      retryCount: '3',
      failureAction: '停止',
      logLevel: 'INFO',
    },
  },
])

// Task instances data
const taskInstances = ref({
  1: [
    {
      id: '1-1',
      status: 'completed',
      startTime: '2024-01-16T10:00:00',
      endTime: '2024-01-16T10:25:00',
    },
    {
      id: '1-2',
      status: 'completed',
      startTime: '2024-01-15T10:00:00',
      endTime: '2024-01-15T10:28:00',
    },
    {
      id: '1-3',
      status: 'failed',
      startTime: '2024-01-14T10:00:00',
      endTime: '2024-01-14T10:15:00',
    },
  ],
  2: [
    { id: '2-1', status: 'running', startTime: '2024-01-16T16:00:00', endTime: null },
    {
      id: '2-2',
      status: 'completed',
      startTime: '2024-01-16T15:00:00',
      endTime: '2024-01-16T15:35:00',
    },
    {
      id: '2-3',
      status: 'completed',
      startTime: '2024-01-16T14:00:00',
      endTime: '2024-01-16T14:38:00',
    },
  ],
  3: [
    {
      id: '3-1',
      status: 'failed',
      startTime: '2024-01-15T09:00:00',
      endTime: '2024-01-15T09:45:00',
    },
    {
      id: '3-2',
      status: 'completed',
      startTime: '2024-01-08T09:00:00',
      endTime: '2024-01-08T09:52:00',
    },
  ],
  4: [
    {
      id: '4-1',
      status: 'completed',
      startTime: '2024-01-15T11:00:00',
      endTime: '2024-01-15T11:18:00',
    },
    {
      id: '4-2',
      status: 'completed',
      startTime: '2024-01-14T11:00:00',
      endTime: '2024-01-14T11:22:00',
    },
    {
      id: '4-3',
      status: 'completed',
      startTime: '2024-01-13T11:00:00',
      endTime: '2024-01-13T11:19:00',
    },
  ],
})

const searchQuery = ref('')
const showConfigPopup = ref(false)
const selectedTask = ref(null)
const expandedTasks = ref([])

tasks.value.forEach((task) => {
  if (typeof task.online === 'undefined') task.online = false
})

const toggleOnline = (task) => {
  task.online = !task.online
}

const filteredTasks = computed(() => {
  let filtered = tasks.value
  if (searchQuery.value) {
    filtered = filtered.filter(
      (t) => t.name.includes(searchQuery.value) || t.type.includes(searchQuery.value),
    )
  }
  // Sort by updatedAt in descending order (newest first)
  return filtered.sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt))
})

const toggleTaskExpansion = (taskId) => {
  const index = expandedTasks.value.indexOf(taskId)
  if (index > -1) {
    expandedTasks.value.splice(index, 1)
  } else {
    expandedTasks.value.push(taskId)
  }
}

const getTaskInstances = (taskId) => {
  return taskInstances.value[taskId] || []
}

const showConfig = (task) => {
  selectedTask.value = task
  showConfigPopup.value = true
}

const closeConfig = () => {
  showConfigPopup.value = false
  selectedTask.value = null
}

const instanceStatusText = (status) => {
  return (
    {
      completed: '已完成',
      running: '运行中',
      failed: '失败',
      paused: '已暂停',
    }[status] || status
  )
}

const viewInstance = (instanceId) => {
  // Find the taskId for this instance
  const taskEntry = Object.entries(taskInstances.value).find(([tid, instances]) =>
    instances.some((inst) => inst.id === instanceId),
  )
  const taskId = taskEntry ? taskEntry[0] : null
  if (taskId) {
    router.push({ name: 'ResultView', params: { taskId, instanceId } })
  } else {
    console.warn('Task ID not found for instance:', instanceId)
  }
}

const rerunInstance = (instanceId) => {
  console.log('Rerunning instance:', instanceId)
  // TODO: Implement rerun logic
}

const pauseInstance = (instanceId) => {
  console.log('Pausing instance:', instanceId)
  // TODO: Implement pause logic
}

const deleteInstance = (instanceId) => {
  console.log('Deleting instance:', instanceId)
  // TODO: Implement delete logic
}

const showTaskDetails = (task) => {
  console.log('Showing task details:', task)
  // TODO: Implement task details view
}

const executeTask = (task) => {
  console.log('Executing task:', task)
  // TODO: Implement task execution
}

const deleteTask = (taskId) => {
  console.log('Deleting task:', taskId)
  // TODO: Implement task deletion
}

const statusText = (status) => {
  return (
    {
      completed: '已完成',
      running: '运行中',
      failed: '失败',
    }[status] || status
  )
}

const statusColor = (status) => {
  return (
    {
      completed: '#42b983',
      running: '#1976d2',
      failed: '#e53935',
    }[status] || '#bbb'
  )
}

const formatDate = (dateStr) => {
  if (!dateStr) return '-'
  const d = new Date(dateStr)
  return d.toLocaleString('zh-CN', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  })
}

// TODO: Connect to backend API here when available
</script>

<style scoped>
.task-view-outer {
  width: 100vw;
  min-height: 80vh;
  background: #f5f8fc;
  display: flex;
  justify-content: center;
}
.task-view-layout {
  display: flex;
  width: 100%;
  max-width: 1400px;
  min-height: 80vh;
  background: transparent;
}
.task-view-main {
  flex: 1;
  padding: 48px 64px;
  display: flex;
  flex-direction: column;
  gap: 32px;
  min-width: 0;
  box-sizing: border-box;
  align-items: center;
  margin-left: 220px;
}
.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-bottom: 32px;
}

.task-title {
  font-size: 2rem;
  font-weight: bold;
  color: #1976d2;
  margin: 0;
}
.task-search-bar {
  width: 300px;
  display: flex;
  align-items: center;
  background: #fff;
  border: 1.5px solid #1976d2;
  border-radius: 8px;
  box-shadow: 0 2px 8px 0 rgba(25, 118, 210, 0.06);
  padding: 0 12px;
  position: relative;
}
.search-icon {
  display: flex;
  align-items: center;
  margin-right: 8px;
}
.search-input {
  flex: 1;
  padding: 10px 8px 10px 0;
  border: none;
  outline: none;
  font-size: 15px;
  background: transparent;
  color: #222;
}
.search-input::placeholder {
  color: #888;
  opacity: 1;
}
.search-input:focus {
  background: #f8fafd;
}
.task-table-wrapper {
  width: 100%;
  max-width: 1200px;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 24px 0 rgba(0, 32, 72, 0.08);
  padding: 0 0 24px 0;
  overflow-x: auto;
}
.task-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 0;
}
.task-table th,
.task-table td {
  padding: 14px 12px;
  border-bottom: 1px solid #e0e0e0;
  text-align: center;
  font-size: 15px;
  color: #222;
}
.task-table th {
  background: #f8fafd;
  color: #1976d2;
  font-weight: 600;
}
.status-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  justify-content: center;
}
.status-dot {
  display: flex;
  align-items: center;
  justify-content: center;
}
.status-label {
  font-size: 14px;
  color: #222;
  font-weight: 500;
}
.action-circles {
  display: flex;
  gap: 8px;
  justify-content: center;
}
.circle-btn {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  border: none;
  background: #f5f7fa;
  color: #1976d2;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition:
    background 0.2s,
    color 0.2s;
  box-shadow: 0 1px 2px rgba(25, 118, 210, 0.04);
}
.circle-btn:hover {
  background: #eaf3ff;
  color: #1251a3;
}
.details-btn svg {
  stroke: #1976d2;
}
.execute-btn svg {
  fill: #42b983;
}
.delete-btn svg {
  stroke: #e53935;
}
.empty-row {
  color: #222;
  font-size: 15px;
  text-align: center;
  padding: 32px 0;
}

/* Configuration Button */
.config-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border: 1px solid #1976d2;
  border-radius: 6px;
  background: #fff;
  color: #1976d2;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.config-btn:hover {
  background: #eaf3ff;
  border-color: #1251a3;
  color: #1251a3;
}

/* Configuration Popup */
.config-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.config-popup {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
}

.config-popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e0e0e0;
}

.config-popup-header h3 {
  margin: 0;
  color: #1976d2;
  font-size: 18px;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background 0.2s;
}

.close-btn:hover {
  background: #f5f5f5;
}

.config-popup-content {
  padding: 24px;
}

.config-section {
  margin-bottom: 24px;
}

.config-section h4 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 8px;
}

.config-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f5f5f5;
}

.config-item:last-child {
  border-bottom: none;
}

.config-label {
  color: #666;
  font-size: 14px;
  font-weight: 500;
}

.config-value {
  color: #333;
  font-size: 14px;
  font-weight: 600;
}

/* Expand Button */
.expand-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.expand-btn:hover {
  background: #f5f5f5;
}

.expand-btn.expanded svg {
  transform: rotate(90deg);
}

.expand-btn svg {
  transition: transform 0.2s;
}

/* Instance Row */
.instance-row {
  background: #f8fafd;
}

.instance-cell {
  padding: 0 !important;
  border: none !important;
}

.instance-container {
  padding: 10px;
  background: #f8fafd;
}

.instance-title {
  margin: 0 0 16px 0;
  color: #1976d2;
  font-size: 16px;
  font-weight: 600;
}

.instance-table {
  width: 100%;
  border-collapse: collapse;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.instance-table th,
.instance-table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #e0e0e0;
  font-size: 14px;
}

.instance-table th {
  background: #f5f7fa;
  color: #1976d2;
  font-weight: 600;
  font-size: 13px;
}

.instance-table tr:last-child td {
  border-bottom: none;
}

.instance-status {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  display: inline-block;
  min-width: 60px;
}

.instance-status.completed {
  background: #e8f5e8;
  color: #2e7d32;
}

.instance-status.running {
  background: #e3f2fd;
  color: #1976d2;
}

.instance-status.failed {
  background: #ffebee;
  color: #c62828;
}

.instance-status.paused {
  background: #fff3e0;
  color: #f57c00;
}

.instance-actions {
  display: flex;
  gap: 6px;
  justify-content: flex-start;
}

.instance-btn {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: none;
  background: #f5f7fa;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.instance-btn:hover {
  background: #eaf3ff;
  transform: scale(1.1);
}

.instance-btn.view-btn:hover {
  background: #e3f2fd;
}

.instance-btn.rerun-btn:hover {
  background: #fff3e0;
}

.instance-btn.pause-btn:hover {
  background: #e3f2fd;
}

.instance-btn.delete-btn:hover {
  background: #ffebee;
}

.empty-instance {
  color: #666;
  font-size: 14px;
  text-align: center;
  padding: 20px 0;
  font-style: italic;
}
@media (max-width: 1100px) {
  .task-view-main {
    padding: 32px 8px;
    margin-left: 220px;
  }
  .task-table-wrapper {
    padding: 0 0 12px 0;
  }
}
@media (max-width: 700px) {
  .task-view-layout {
    flex-direction: column;
    align-items: stretch;
  }
  .task-view-main {
    padding: 8px 2px;
    gap: 16px;
    margin-left: 0;
  }
  .task-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  .task-search-bar {
    width: 100%;
  }
  .task-table-wrapper {
    max-width: 98vw;
    border-radius: 8px;
    box-shadow: 0 2px 8px 0 rgba(0, 32, 72, 0.08);
  }
  .task-title {
    font-size: 1.3rem;
  }
}
</style>
