<template>
  <div class="user-center-outer">
    <div class="user-center-layout">
      <UserNavBar />
      <div class="user-center-main">
        <section class="user-info-card card">
          <div class="user-info-icon">
            <svg width="64" height="64" viewBox="0 0 64 64" fill="none">
              <circle cx="32" cy="22" r="14" stroke="#1976d2" stroke-width="3" />
              <path d="M10 54c0-10 10-18 22-18s22 8 22 18" stroke="#1976d2" stroke-width="3" />
            </svg>
          </div>
          <div class="user-info-details">
            <div class="user-info-row">
              <span class="label">用户名</span><span class="value">{{ username || '未登录' }}</span>
            </div>
            <div class="user-info-row">
              <span class="label">所属机构</span><span class="value">总行</span>
            </div>
            <div class="user-info-row">
              <span class="label">用户角色</span><span class="value">系统管理员</span>
            </div>
          </div>
        </section>
        <section class="user-stats-card card">
          <div class="stat-block">
            <div class="stat-icon stat-blue">
              <svg width="40" height="40" viewBox="0 0 24 24" fill="none">
                <rect x="4" y="4" width="16" height="16" rx="3" fill="#eaf3ff" />
                <path d="M8 8h8M8 12h8M8 16h4" stroke="#1976d2" stroke-width="2" />
              </svg>
            </div>
            <div class="stat-label">任务总量</div>
            <div class="stat-value">35,019</div>
          </div>
          <div class="stat-block">
            <div class="stat-icon stat-purple">
              <svg width="40" height="40" viewBox="0 0 24 24" fill="none">
                <rect x="4" y="4" width="16" height="16" rx="3" fill="#f3eaff" />
                <path d="M8 8h8M8 12h8M8 16h4" stroke="#a259ec" stroke-width="2" />
              </svg>
            </div>
            <div class="stat-label">已完成任务数</div>
            <div class="stat-value">6,231</div>
          </div>
          <div class="stat-block">
            <div class="stat-icon stat-orange">
              <svg width="40" height="40" viewBox="0 0 24 24" fill="none">
                <circle cx="12" cy="12" r="10" fill="#fff7ea" />
                <circle cx="12" cy="12" r="8" fill="#fff7ea" />
                <circle cx="12" cy="12" r="6" fill="#fff7ea" />
                <circle cx="12" cy="12" r="4" fill="#ff9800" />
              </svg>
            </div>
            <div class="stat-label">进行中任务数</div>
            <div class="stat-value">678</div>
          </div>
        </section>
        <section class="logout-card card">
          <button class="logout-btn" @click="handleLogout">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path
                d="M16 17l5-5-5-5M21 12H9"
                stroke="#1976d2"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <rect x="3" y="4" width="6" height="16" rx="2" stroke="#1976d2" stroke-width="2" />
            </svg>
            <span>登出</span>
          </button>
        </section>
      </div>
    </div>
  </div>
</template>

<script setup>
import UserNavBar from '../components/common/UserNavBar.vue'
import { useStates } from '@/store/states'
import { useRouter } from 'vue-router'
import { computed } from 'vue'

const statesStore = useStates()
const router = useRouter()
const username = computed(() => statesStore.username)

function handleLogout() {
  statesStore.clearAllAuthData()
  router.push('/login')
}
</script>

<style scoped>
.user-center-outer {
  width: 100vw;
  min-height: 80vh;
  background: #f5f8fc;
  display: flex;
  justify-content: center;
}
.user-center-layout {
  display: flex;
  width: 100%;
  max-width: 1400px;
  min-height: 80vh;
  background: transparent;
}
.user-center-main {
  flex: 1;
  padding: 48px 64px;
  display: flex;
  flex-direction: column;
  gap: 32px;
  min-width: 0;
  box-sizing: border-box;
  align-items: center;
  margin-left: 220px;
}
.card {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 24px 0 rgba(0, 32, 72, 0.08);
  padding: 32px 48px;
  width: 100%;
  max-width: 700px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.user-info-card {
  gap: 48px;
  margin-top: 24px;
}
.user-info-icon {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
}
.user-info-details {
  display: flex;
  flex-direction: column;
  gap: 18px;
}
.user-info-row {
  display: flex;
  gap: 32px;
  font-size: 18px;
  align-items: center;
}
.label {
  color: #1976d2;
  font-weight: 500;
  min-width: 90px;
}
.value {
  color: #222;
  font-weight: 500;
}
.user-stats-card {
  justify-content: space-between;
  gap: 32px;
  margin-top: 8px;
}
.stat-block {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  min-width: 180px;
}
.stat-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  margin-bottom: 8px;
}
.stat-blue {
  background: #eaf3ff;
}
.stat-purple {
  background: #f3eaff;
}
.stat-orange {
  background: #fff7ea;
}
.stat-label {
  color: #888;
  font-size: 16px;
  margin-bottom: 2px;
}
.stat-value {
  font-size: 28px;
  color: #222;
  font-weight: bold;
}
.logout-card {
  justify-content: center;
  margin-top: 8px;
}
.logout-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: none;
  border: none;
  color: #1976d2;
  font-size: 18px;
  font-weight: 500;
  cursor: pointer;
  padding: 8px 32px;
  border-radius: 8px;
  transition: background 0.2s;
}
.logout-btn:hover {
  background: #eaf3ff;
}
@media (max-width: 1100px) {
  .user-center-main {
    padding: 32px 8px;
    margin-left: 220px;
  }
  .card {
    padding: 24px 8px;
  }
  .user-info-card {
    gap: 24px;
  }
  .user-stats-card {
    gap: 12px;
  }
}
@media (max-width: 700px) {
  .user-center-layout {
    flex-direction: column;
    align-items: stretch;
  }
  .user-center-main {
    padding: 8px 2px;
    gap: 16px;
    margin-left: 0;
  }
  .card {
    padding: 16px 2px;
    max-width: 98vw;
  }
  .user-info-card {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  .user-info-row {
    flex-direction: column;
    gap: 4px;
    align-items: flex-start;
  }
  .user-stats-card {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  .stat-block {
    min-width: 0;
  }
}
</style>
