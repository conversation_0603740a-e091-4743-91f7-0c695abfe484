<template>
  <div class="chatview-outer">
    <div class="chatview-container">
      <div class="chatview-left">
        <ChatMain ref="chatMain" @analysis-result="handleAnalysisResult" />
      </div>
      <div class="chatview-right">
        <ResultPanel :results="analysisResults" />
      </div>
    </div>
  </div>
</template>

<script>
import ChatMain from '../components/chat/ChatMain.vue'
import ResultPanel from '../components/ResultPanel.vue'
import { ref } from 'vue'
import { useStates } from '@/store/states'

export default {
  name: 'ChatView',
  components: {
    ChatMain,
    ResultPanel,
  },
  setup() {
    const analysisResults = ref([])
    function handleAnalysisResult(result) {
      if (result) analysisResults.value.push(result)
    }
    return { analysisResults, handleAnalysisResult }
  },
  mounted() {
    const statesStore = useStates()
    statesStore.$patch({ pageName: '对话式', title: '对话式分析' })
  },
}
</script>

<style>
.chatview-outer {
  width: 100vw;
  height: calc(100vh - 128px);
  background: #f5f6fa;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
}

.chatview-container {
  display: flex;
  flex-direction: row;
  background: transparent;
  /* max-width: 1600px; */
  width: 100vw;
  height: 100%;
  box-sizing: border-box;
  align-items: center;
  justify-content: center;
  gap: 32px;
}

.chatview-left,
.chatview-right {
  width: 1000px;
  max-width: 48vw;
  min-width: 320px;
  height: 90vh;
  max-height: 100%;
  background: none;
  display: flex;
  flex-direction: column;
  overflow: auto;
  box-sizing: border-box;
}

.chatview-left {
  border-right: 1px solid #e0e0e0;
  background: #fff;
  border-radius: 18px 0 0 18px;
  box-shadow: 0 4px 24px rgba(80, 120, 200, 0.06);
}

.chatview-right {
  background: #f7faff;
  border-radius: 0 18px 18px 0;
  box-shadow: 0 4px 24px rgba(80, 120, 200, 0.06);
}

@media (max-width: 1200px) {
  .chatview-container {
    max-width: 100vw;
    gap: 12px;
  }
  .chatview-left,
  .chatview-right {
    width: 48vw;
    min-width: 0;
    max-width: 100vw;
  }
}

@media (max-width: 900px) {
  .chatview-container {
    flex-direction: column;
    align-items: stretch;
    justify-content: flex-start;
    height: 100%;
    gap: 18px;
  }
  .chatview-left,
  .chatview-right {
    width: 100vw;
    max-width: 100vw;
    min-width: 0;
    height: 48vh;
    max-height: 48vh;
    border-radius: 18px;
  }
  .chatview-left {
    border-right: none;
    border-radius: 18px 18px 0 0;
  }
  .chatview-right {
    border-radius: 0 0 18px 18px;
  }
}

@media (max-width: 600px) {
  .chatview-container {
    padding: 0;
    gap: 8px;
  }
  .chatview-left,
  .chatview-right {
    width: 100vw;
    min-width: 0;
    max-width: 100vw;
    height: 50vh;
    max-height: 50vh;
    border-radius: 12px;
  }
}

/* 重置一些基础样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: Arial, sans-serif;
  line-height: 1.6;
}
</style>
