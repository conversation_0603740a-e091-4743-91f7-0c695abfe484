<template>
  <div class="login-outer">
    <div class="login-card card">
      <div class="login-header">
        <svg width="48" height="48" viewBox="0 0 64 64" fill="none">
          <circle cx="32" cy="22" r="14" stroke="#1976d2" stroke-width="3" />
          <path d="M10 54c0-10 10-18 22-18s22 8 22 18" stroke="#1976d2" stroke-width="3" />
        </svg>
        <h2>用户登录</h2>
      </div>
      <form class="login-form" @submit.prevent="onLogin">
        <div class="form-group">
          <label for="username">用户名</label>
          <input
            id="username"
            v-model="username"
            type="text"
            placeholder="请输入用户名"
            required
            autocomplete="username"
          />
        </div>
        <div class="form-group">
          <label for="password">密码</label>
          <input
            id="password"
            v-model="password"
            type="password"
            placeholder="请输入密码"
            required
            autocomplete="current-password"
          />
        </div>
        <div v-if="error" class="error-msg">{{ error }}</div>
        <button class="login-btn" type="submit" :disabled="loading">
          <span v-if="loading">登录中...</span>
          <span v-else>登录</span>
        </button>
      </form>
    </div>
    <div v-if="showNotification" class="notification-toast" :class="notificationType">
      <span class="notification-icon">
        {{ notificationType === 'success' ? '✅' : '❌' }}
      </span>
      <span class="notification-message">{{ notificationMessage }}</span>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useStates } from '@/store/states'
import axios from 'axios'
import { handleError } from '@/utils/errorHandler'

// MD5 hash function
function md5(str) {
  function rotateLeft(value, amount) {
    const lbits = (value << amount) | (value >>> (32 - amount))
    return lbits
  }

  function addUnsigned(x, y) {
    const x4 = x & 0x40000000
    const y4 = y & 0x40000000
    const x8 = x & 0x80000000
    const y8 = y & 0x80000000
    const result = (x & 0x3fffffff) + (y & 0x3fffffff)
    if (x4 & y4) {
      return result ^ 0x80000000 ^ x8 ^ y8
    }
    if (x4 | y4) {
      if (result & 0x40000000) {
        return result ^ 0xc0000000 ^ x8 ^ y8
      } else {
        return result ^ 0x40000000 ^ x8 ^ y8
      }
    } else {
      return result ^ x8 ^ y8
    }
  }

  function f(x, y, z) {
    return (x & y) | (~x & z)
  }
  function g(x, y, z) {
    return (x & z) | (y & ~z)
  }
  function h(x, y, z) {
    return x ^ y ^ z
  }
  function i(x, y, z) {
    return y ^ (x | ~z)
  }

  function ff(a, b, c, d, x, s, ac) {
    a = addUnsigned(a, addUnsigned(addUnsigned(f(b, c, d), x), ac))
    return addUnsigned(rotateLeft(a, s), b)
  }

  function gg(a, b, c, d, x, s, ac) {
    a = addUnsigned(a, addUnsigned(addUnsigned(g(b, c, d), x), ac))
    return addUnsigned(rotateLeft(a, s), b)
  }

  function hh(a, b, c, d, x, s, ac) {
    a = addUnsigned(a, addUnsigned(addUnsigned(h(b, c, d), x), ac))
    return addUnsigned(rotateLeft(a, s), b)
  }

  function ii(a, b, c, d, x, s, ac) {
    a = addUnsigned(a, addUnsigned(addUnsigned(i(b, c, d), x), ac))
    return addUnsigned(rotateLeft(a, s), b)
  }

  function convertToWordArray(str) {
    let wordArray = []
    let wordCount = ((str.length + 8 - ((str.length + 8) % 64)) / 64 + 1) * 16
    for (let i = 0; i < wordCount - 1; i++) {
      wordArray[i] = 0
    }
    let bytePosition = 0
    let byteCount = 0
    while (byteCount < str.length) {
      wordArray[bytePosition] =
        wordArray[bytePosition] | (str.charCodeAt(byteCount) << (8 * (byteCount % 4)))
      if (byteCount % 4 === 3) {
        bytePosition++
      }
      byteCount++
    }
    wordArray[bytePosition] = wordArray[bytePosition] | (0x80 << (8 * (byteCount % 4)))
    wordArray[wordCount - 2] = str.length << 3
    wordArray[wordCount - 1] = str.length >>> 29
    return wordArray
  }

  function wordToHex(value) {
    let hex = ''
    for (let i = 0; i <= 3; i++) {
      const byte = (value >>> (i * 8)) & 255
      hex += ('0' + byte.toString(16)).slice(-2)
    }
    return hex
  }

  const wordArray = convertToWordArray(str)
  let h0 = 0x67452301
  let h1 = 0xefcdab89
  let h2 = 0x98badcfe
  let h3 = 0x10325476

  for (let i = 0; i < wordArray.length; i += 16) {
    const aa = h0,
      bb = h1,
      cc = h2,
      dd = h3

    h0 = ff(h0, h1, h2, h3, wordArray[i + 0], 7, 0xd76aa478)
    h3 = ff(h3, h0, h1, h2, wordArray[i + 1], 12, 0xe8c7b756)
    h2 = ff(h2, h3, h0, h1, wordArray[i + 2], 17, 0x242070db)
    h1 = ff(h1, h2, h3, h0, wordArray[i + 3], 22, 0xc1bdceee)
    h0 = ff(h0, h1, h2, h3, wordArray[i + 4], 7, 0xf57c0faf)
    h3 = ff(h3, h0, h1, h2, wordArray[i + 5], 12, 0x4787c62a)
    h2 = ff(h2, h3, h0, h1, wordArray[i + 6], 17, 0xa8304613)
    h1 = ff(h1, h2, h3, h0, wordArray[i + 7], 22, 0xfd469501)
    h0 = ff(h0, h1, h2, h3, wordArray[i + 8], 7, 0x698098d8)
    h3 = ff(h3, h0, h1, h2, wordArray[i + 9], 12, 0x8b44f7af)
    h2 = ff(h2, h3, h0, h1, wordArray[i + 10], 17, 0xffff5bb1)
    h1 = ff(h1, h2, h3, h0, wordArray[i + 11], 22, 0x895cd7be)
    h0 = ff(h0, h1, h2, h3, wordArray[i + 12], 7, 0x6b901122)
    h3 = ff(h3, h0, h1, h2, wordArray[i + 13], 12, 0xfd987193)
    h2 = ff(h2, h3, h0, h1, wordArray[i + 14], 17, 0xa679438e)
    h1 = ff(h1, h2, h3, h0, wordArray[i + 15], 22, 0x49b40821)

    h0 = gg(h0, h1, h2, h3, wordArray[i + 1], 5, 0xf61e2562)
    h3 = gg(h3, h0, h1, h2, wordArray[i + 6], 9, 0xc040b340)
    h2 = gg(h2, h3, h0, h1, wordArray[i + 11], 14, 0x265e5a51)
    h1 = gg(h1, h2, h3, h0, wordArray[i + 0], 20, 0xe9b6c7aa)
    h0 = gg(h0, h1, h2, h3, wordArray[i + 5], 5, 0xd62f105d)
    h3 = gg(h3, h0, h1, h2, wordArray[i + 10], 9, 0x02441453)
    h2 = gg(h2, h3, h0, h1, wordArray[i + 15], 14, 0xd8a1e681)
    h1 = gg(h1, h2, h3, h0, wordArray[i + 4], 20, 0xe7d3fbc8)
    h0 = gg(h0, h1, h2, h3, wordArray[i + 9], 5, 0x21e1cde6)
    h3 = gg(h3, h0, h1, h2, wordArray[i + 14], 9, 0xc33707d6)
    h2 = gg(h2, h3, h0, h1, wordArray[i + 3], 14, 0xf4d50d87)
    h1 = gg(h1, h2, h3, h0, wordArray[i + 8], 20, 0x455a14ed)
    h0 = gg(h0, h1, h2, h3, wordArray[i + 13], 5, 0xa9e3e905)
    h3 = gg(h3, h0, h1, h2, wordArray[i + 2], 9, 0xfcefa3f8)
    h2 = gg(h2, h3, h0, h1, wordArray[i + 7], 14, 0x676f02d9)
    h1 = gg(h1, h2, h3, h0, wordArray[i + 12], 20, 0x8d2a4c8a)

    h0 = hh(h0, h1, h2, h3, wordArray[i + 5], 4, 0xfffa3942)
    h3 = hh(h3, h0, h1, h2, wordArray[i + 8], 11, 0x8771f681)
    h2 = hh(h2, h3, h0, h1, wordArray[i + 11], 16, 0x6d9d6122)
    h1 = hh(h1, h2, h3, h0, wordArray[i + 14], 23, 0xfde5380c)
    h0 = hh(h0, h1, h2, h3, wordArray[i + 1], 4, 0xa4beea44)
    h3 = hh(h3, h0, h1, h2, wordArray[i + 4], 11, 0x4bdecfa9)
    h2 = hh(h2, h3, h0, h1, wordArray[i + 7], 16, 0xf6bb4b60)
    h1 = hh(h1, h2, h3, h0, wordArray[i + 10], 23, 0xbebfbc70)
    h0 = hh(h0, h1, h2, h3, wordArray[i + 13], 4, 0x289b7ec6)
    h3 = hh(h3, h0, h1, h2, wordArray[i + 0], 11, 0xeaa127fa)
    h2 = hh(h2, h3, h0, h1, wordArray[i + 3], 16, 0xd4ef3085)
    h1 = hh(h1, h2, h3, h0, wordArray[i + 6], 23, 0x04881d05)
    h0 = hh(h0, h1, h2, h3, wordArray[i + 9], 4, 0xd9d4d039)
    h3 = hh(h3, h0, h1, h2, wordArray[i + 12], 11, 0xe6db99e5)
    h2 = hh(h2, h3, h0, h1, wordArray[i + 15], 16, 0x1fa27cf8)
    h1 = hh(h1, h2, h3, h0, wordArray[i + 2], 23, 0xc4ac5665)

    h0 = ii(h0, h1, h2, h3, wordArray[i + 0], 6, 0xf4292244)
    h3 = ii(h3, h0, h1, h2, wordArray[i + 7], 10, 0x432aff97)
    h2 = ii(h2, h3, h0, h1, wordArray[i + 14], 15, 0xab9423a7)
    h1 = ii(h1, h2, h3, h0, wordArray[i + 5], 21, 0xfc93a039)
    h0 = ii(h0, h1, h2, h3, wordArray[i + 12], 6, 0x655b59c3)
    h3 = ii(h3, h0, h1, h2, wordArray[i + 3], 10, 0x8f0ccc92)
    h2 = ii(h2, h3, h0, h1, wordArray[i + 10], 15, 0xffeff47d)
    h1 = ii(h1, h2, h3, h0, wordArray[i + 1], 21, 0x85845dd1)
    h0 = ii(h0, h1, h2, h3, wordArray[i + 8], 6, 0x6fa87e4f)
    h3 = ii(h3, h0, h1, h2, wordArray[i + 15], 10, 0xfe2ce6e0)
    h2 = ii(h2, h3, h0, h1, wordArray[i + 6], 15, 0xa3014314)
    h1 = ii(h1, h2, h3, h0, wordArray[i + 13], 21, 0x4e0811a1)
    h0 = ii(h0, h1, h2, h3, wordArray[i + 4], 6, 0xf7537e82)
    h3 = ii(h3, h0, h1, h2, wordArray[i + 11], 10, 0xbd3af235)
    h2 = ii(h2, h3, h0, h1, wordArray[i + 2], 15, 0x2ad7d2bb)
    h1 = ii(h1, h2, h3, h0, wordArray[i + 9], 21, 0xeb86d391)

    h0 = addUnsigned(h0, aa)
    h1 = addUnsigned(h1, bb)
    h2 = addUnsigned(h2, cc)
    h3 = addUnsigned(h3, dd)
  }

  return (wordToHex(h0) + wordToHex(h1) + wordToHex(h2) + wordToHex(h3)).toLowerCase()
}

const username = ref('')
const password = ref('')
const error = ref('')
const loading = ref(false)
const router = useRouter()
const statesStore = useStates()

const showNotification = ref(false)
const notificationMessage = ref('')
const notificationType = ref('success')

const showToast = (message, type = 'success', duration = 10000) => {
  notificationMessage.value = message
  notificationType.value = type
  showNotification.value = true

  // Auto-close after specified duration
  setTimeout(() => {
    showNotification.value = false
  }, duration)
}

async function onLogin() {
  error.value = ''

  if (!username.value || !password.value) {
    error.value = '请输入用户名和密码'
    return
  }

  loading.value = true

  try {
    // Hash the password with MD5
    const hashedPassword = md5(password.value)

    // Prepare the request payload
    const requestPayload = {
      username: username.value,
      password: hashedPassword,
    }

    console.log('Posting to /api/v1/accounts/auth/login:', requestPayload)

    // Make API call to login endpoint
    const response = await axios.post('/api/v1/accounts/auth/login', requestPayload)
    console.log('Responding from /api/v1/accounts/auth/login:', response)

    const data = response.data

    // Check if login was successful
    if (data.error_code === 0) {
      // Login successful
      const { token, expire_time, user_info } = data.data

      // Store user information in the store
      statesStore.setUsername(user_info.username)
      statesStore.setUserRole(user_info.role)
      statesStore.setAuthToken(token)
      statesStore.setTokenExpireTime(expire_time)
      statesStore.setUserInfo(user_info)

      // Also store in localStorage for persistence
      localStorage.setItem('auth_token', token)
      localStorage.setItem('token_expire_time', expire_time)
      localStorage.setItem('user_info', JSON.stringify(user_info))

      showToast('登录成功！', 'success')

      setTimeout(() => {
        router.push('/')
      }, 1000)
    } else {
      // Login failed - handle different error codes
      let errorMessage = data.message || '登录失败'

      switch (data.error_code) {
        case -1:
          errorMessage = '用户不存在'
          break
        case -2:
          errorMessage = '用户名或密码错误'
          break
        case -3:
          errorMessage = '资源不足，请稍后重试'
          break
        case 101:
          errorMessage = data.message || '用户名不存在或密码错误'
          break
        default:
          errorMessage = data.message || '登录失败，请重试'
      }

      error.value = errorMessage
      showToast(errorMessage, 'error')
    }
  } catch (err) {
    console.error('Login error:', err)

    // Handle network errors or other exceptions
    let errorMessage = '登录失败'
    if (err.response) {
      // Server responded with error status
      const errorData = err.response.data
      errorMessage = errorData.message || '登录失败，请检查网络连接'
    } else if (err.request) {
      // Network error
      errorMessage = '网络连接失败，请检查网络设置'
    } else {
      // Other error
      errorMessage = '登录失败，请重试'
    }

    error.value = errorMessage

    // Use the new error handler to classify and display errors appropriately
    const retryAction = () => onLogin() // Allow retry for system errors
    handleError(err, errorMessage, retryAction, showToast)
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.login-outer {
  min-height: 80vh;
  display: flex;
  align-items: center;
  justify-content: center;
}
.login-card {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 24px 0 rgba(0, 32, 72, 0.08);
  padding: 48px 40px 40px 40px;
  width: 100%;
  max-width: 400px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.login-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  margin-bottom: 32px;
}
.login-header h2 {
  color: #1976d2;
  font-size: 26px;
  font-weight: 600;
  margin: 0;
}
.login-form {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 20px;
}
.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}
.form-group label {
  color: #1976d2;
  font-weight: 500;
  font-size: 15px;
}
.form-group input {
  padding: 10px 12px;
  border: 1px solid #e0e7ef;
  border-radius: 8px;
  font-size: 16px;
  outline: none;
  transition: border 0.2s;
}
.form-group input:focus {
  border-color: #1976d2;
}
.error-msg {
  color: #e53935;
  background: #fff0f0;
  border-radius: 6px;
  padding: 6px 12px;
  font-size: 14px;
  margin-bottom: -8px;
  text-align: center;
}
.login-btn {
  background: #1976d2;
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 12px 0;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s;
}
.login-btn:hover:not(:disabled) {
  background: #1251a3;
}
.login-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
  opacity: 0.6;
}
@media (max-width: 600px) {
  .login-card {
    padding: 24px 8px 24px 8px;
    max-width: 98vw;
  }
}
.notification-toast {
  position: fixed;
  top: 20px;
  right: 20px;
  background-color: #fff;
  border-radius: 8px;
  padding: 12px 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  gap: 8px;
  z-index: 1000;
  border-left: 4px solid #4caf50;
  animation: slideIn 0.3s ease;
}
@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}
.notification-icon {
  font-size: 18px;
}
.notification-message {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}
.notification-toast.success {
  border-left-color: #4caf50;
}
.notification-toast.error {
  border-left-color: #f44336;
}
</style>
