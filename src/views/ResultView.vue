<template>
  <div class="resultview-root">
    <UserNavBar />
    <div class="resultview-main">
      <div class="resultview-header">
        <h1 class="resultview-title">个贷催收场景分析 - 结果报告</h1>
        <div class="resultview-meta">
          <span>分析周期：2024年1月-2024年6月</span>
          <span>报告生成时间：2024-06-12 10:30</span>
        </div>
        <button class="download-btn" @click="downloadReport" title="下载报告 (PDF)">
          <svg width="22" height="22" viewBox="0 0 24 24" fill="none">
            <path
              d="M12 3v14m0 0l-5-5m5 5l5-5"
              stroke="#fff"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <rect x="4" y="19" width="16" height="2" rx="1" fill="#fff" />
          </svg>
          <span>下载报告</span>
        </button>
      </div>

      <div class="resultview-section">
        <div class="section-title">一、AI 智能结论</div>
        <div class="ai-summary-box">
          <div class="ai-summary-title">AI 智能归因结论</div>
          <div class="ai-summary-content">
            <p>
              本周期内，个贷催收整体回款率提升至 <b>87.3%</b>，较上期增长 <b>4.2%</b>。AI
              归因分析显示，催收成功率提升主要受以下因素驱动：
            </p>
            <ul>
              <li>催收策略优化（贡献度 38%）：智能分案与多渠道触达提升了高风险客户的回款概率。</li>
              <li>客户画像细分（贡献度 27%）：针对不同客户群体定制催收话术，提升了响应率。</li>
              <li>外部经济环境改善（贡献度 19%）：宏观经济回暖带动客户还款意愿增强。</li>
            </ul>
            <p>建议持续优化智能分案模型，并加强对高风险客户的精准触达。</p>
          </div>
        </div>
      </div>

      <div class="resultview-section">
        <div class="section-title">二、经营趋势分析</div>
        <div class="trend-summary-row">
          <div class="trend-summary-card">
            <div class="trend-label">回款率（月度）</div>
            <div class="trend-value">87.3%</div>
            <div class="trend-desc">较去年同期提升 4.2%</div>
          </div>
          <div class="trend-summary-card">
            <div class="trend-label">逾期余额</div>
            <div class="trend-value">1.28亿元</div>
            <div class="trend-desc">同比下降 7.5%</div>
          </div>
          <div class="trend-summary-card">
            <div class="trend-label">催收成功率</div>
            <div class="trend-value">62.1%</div>
            <div class="trend-desc">环比提升 2.8%</div>
          </div>
        </div>
        <div class="trend-chart-section">
          <v-chart class="trend-echart" :option="trendChartOption" autoresize />
          <div class="trend-chart-caption">图1：2024年上半年催收回款率月度趋势</div>
        </div>
      </div>

      <div class="resultview-section">
        <div class="section-title">三、异动分析</div>
        <div class="abnormal-box">
          <div class="abnormal-title">4月回款率异动预警</div>
          <div class="abnormal-content">
            <p>
              2024年4月回款率出现短暂下滑（82.5%），较3月下降5.1个百分点。AI 检测到主要原因如下：
            </p>
            <ul>
              <li>部分高风险客户集中逾期，导致回款压力上升。</li>
              <li>节假日影响，催收触达率下降。</li>
            </ul>
            <p>建议：针对高风险客户提前预警，节前加强催收力度。</p>
          </div>
          <v-chart class="abnormal-echart" :option="abnormalChartOption" autoresize />
        </div>
      </div>

      <div class="resultview-section">
        <div class="section-title">四、关键指标维度拆解</div>
        <div class="dimension-box">
          <div class="dimension-title">回款率分客户类型拆解</div>
          <div class="dimension-content">
            <p>
              企业主客户回款率最高（91.2%），白领客户次之（86.7%），蓝领客户相对较低（79.5%）。建议针对蓝领客户群体优化催收策略。
            </p>
          </div>
          <v-chart class="dimension-echart" :option="dimensionChartOption" autoresize />
        </div>
      </div>

      <div class="resultview-section">
        <div class="section-title">五、相关性分析</div>
        <div class="corr-grid">
          <div class="corr-card">
            <div class="corr-label">催收成功率 & 客户年龄</div>
            <div class="corr-value">皮尔逊系数：<b>0.42</b></div>
            <div class="corr-desc">中等正相关，35-50岁客户回款率最高。</div>
          </div>
          <div class="corr-card">
            <div class="corr-label">催收成功率 & 逾期天数</div>
            <div class="corr-value">皮尔逊系数：<b>-0.61</b></div>
            <div class="corr-desc">显著负相关，逾期天数越长，回款概率越低。</div>
          </div>
          <div class="corr-card">
            <div class="corr-label">催收成功率 & 客户信用评分</div>
            <div class="corr-value">皮尔逊系数：<b>0.55</b></div>
            <div class="corr-desc">中等正相关，高信用评分客户更易还款。</div>
          </div>
        </div>
        <div class="corr-chart-section">
          <v-chart class="corr-heatmap" :option="corrHeatmapOption" autoresize />
          <div class="corr-chart-caption">图2：主要变量相关性热力图</div>
        </div>
      </div>

      <div class="resultview-section">
        <div class="section-title">六、数据切片（样例）</div>
        <div class="datasheet-table-wrapper">
          <table class="datasheet-table">
            <thead>
              <tr>
                <th>客户ID</th>
                <th>年龄</th>
                <th>逾期天数</th>
                <th>信用评分</th>
                <th>催收结果</th>
                <th>回款金额</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>100238</td>
                <td>42</td>
                <td>18</td>
                <td>782</td>
                <td>成功</td>
                <td>¥12,000</td>
              </tr>
              <tr>
                <td>100412</td>
                <td>36</td>
                <td>7</td>
                <td>810</td>
                <td>成功</td>
                <td>¥8,500</td>
              </tr>
              <tr>
                <td>100587</td>
                <td>51</td>
                <td>32</td>
                <td>690</td>
                <td>失败</td>
                <td>¥0</td>
              </tr>
              <tr>
                <td>100921</td>
                <td>29</td>
                <td>5</td>
                <td>845</td>
                <td>成功</td>
                <td>¥6,200</td>
              </tr>
              <tr>
                <td>101034</td>
                <td>47</td>
                <td>24</td>
                <td>755</td>
                <td>成功</td>
                <td>¥10,800</td>
              </tr>
            </tbody>
          </table>
        </div>
        <div class="datasheet-caption">表1：部分客户催收数据样例</div>
      </div>

      <div class="resultview-section">
        <div class="section-title">七、综合建议</div>
        <div class="advice-box">
          <ul>
            <li>持续优化智能分案与多渠道触达策略，提升高风险客户回款率。</li>
            <li>加强客户画像细分，针对不同群体定制催收话术与激励措施。</li>
            <li>关注逾期天数较长客户，提前介入，降低坏账风险。</li>
            <li>结合外部经济数据，动态调整催收策略。</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import UserNavBar from '../components/common/UserNavBar.vue'
import { ref } from 'vue'
import VChart from 'vue-echarts'

// 经营趋势分析折线图
const trendChartOption = ref({
  tooltip: { trigger: 'axis' },
  legend: { data: ['回款率'] },
  xAxis: {
    type: 'category',
    data: ['1月', '2月', '3月', '4月', '5月', '6月'],
    axisLabel: { color: '#1976d2', fontWeight: 'bold' },
  },
  yAxis: {
    type: 'value',
    axisLabel: { formatter: '{value}%', color: '#1976d2' },
  },
  series: [
    {
      name: '回款率',
      type: 'line',
      data: [83.1, 85.4, 87.6, 82.5, 88.2, 87.3],
      smooth: true,
      lineStyle: { color: '#1976d2', width: 4 },
      itemStyle: { color: '#42b983' },
      areaStyle: { color: 'rgba(25, 118, 210, 0.08)' },
      symbol: 'circle',
      symbolSize: 10,
      emphasis: { focus: 'series' },
    },
  ],
})

// 异动分析折线图，4月为异动点
const abnormalChartOption = ref({
  tooltip: { trigger: 'axis' },
  xAxis: {
    type: 'category',
    data: ['3月', '4月', '5月'],
    axisLabel: { color: '#1976d2', fontWeight: 'bold' },
  },
  yAxis: {
    type: 'value',
    axisLabel: { formatter: '{value}%', color: '#1976d2' },
  },
  series: [
    {
      name: '回款率',
      type: 'line',
      data: [87.6, 82.5, 88.2],
      smooth: true,
      lineStyle: { color: '#e53935', width: 4 },
      itemStyle: { color: '#e53935' },
      symbol: 'circle',
      symbolSize: 12,
      markPoint: {
        data: [
          {
            name: '异动',
            value: 82.5,
            xAxis: 1,
            yAxis: 82.5,
            itemStyle: { color: '#ff9800' },
            symbol: 'pin',
            symbolSize: 48,
            label: { color: '#fff', fontWeight: 'bold', formatter: '异动' },
          },
        ],
      },
      emphasis: { focus: 'series' },
    },
  ],
})

// 关键指标维度拆解 - ECharts Tree
const dimensionChartOption = ref({
  tooltip: { trigger: 'item', triggerOn: 'mousemove' },
  series: [
    {
      type: 'tree',
      data: [
        {
          name: '客户类型',
          children: [
            {
              name: '企业主',
              value: 91.2,
              label: { formatter: '{b}\n回款率: 91.2%' },
            },
            {
              name: '白领',
              value: 86.7,
              label: { formatter: '{b}\n回款率: 86.7%' },
            },
            {
              name: '蓝领',
              value: 79.5,
              label: { formatter: '{b}\n回款率: 79.5%' },
            },
          ],
        },
      ],
      top: '5%',
      left: '20%',
      bottom: '5%',
      right: '20%',
      symbolSize: 18,
      label: {
        position: 'left',
        verticalAlign: 'middle',
        align: 'right',
        fontSize: 15,
        color: '#1976d2',
      },
      leaves: {
        label: {
          position: 'right',
          verticalAlign: 'middle',
          align: 'left',
          fontSize: 15,
          color: '#42b983',
        },
      },
      lineStyle: {
        color: '#1976d2',
        width: 2,
      },
      emphasis: {
        focus: 'descendant',
      },
      expandAndCollapse: true,
      animationDuration: 550,
      animationDurationUpdate: 750,
    },
  ],
})

// 相关性分析热力图
const corrHeatmapOption = ref({
  tooltip: {
    position: 'top',
    formatter: function (params) {
      return `${params.seriesName}<br/>${params.name} & ${params.value[1]}: <b>${params.value[2]}</b>`
    },
  },
  grid: {
    height: '70%',
    top: '10%',
  },
  xAxis: {
    type: 'category',
    data: ['年龄', '逾期天数', '信用评分', '催收成功率'],
    splitArea: { show: true },
    axisLabel: { color: '#1976d2', fontWeight: 'bold' },
  },
  yAxis: {
    type: 'category',
    data: ['年龄', '逾期天数', '信用评分', '催收成功率'],
    splitArea: { show: true },
    axisLabel: { color: '#1976d2', fontWeight: 'bold' },
  },
  visualMap: {
    min: -1,
    max: 1,
    calculable: true,
    orient: 'horizontal',
    left: 'center',
    bottom: '2%',
    inRange: {
      color: ['#e53935', '#fffde4', '#42b983'],
    },
  },
  series: [
    {
      name: '相关系数',
      type: 'heatmap',
      data: [
        [0, 0, 1],
        [0, 1, -0.18],
        [0, 2, 0.22],
        [0, 3, 0.42],
        [1, 0, -0.18],
        [1, 1, 1],
        [1, 2, -0.33],
        [1, 3, -0.61],
        [2, 0, 0.22],
        [2, 1, -0.33],
        [2, 2, 1],
        [2, 3, 0.55],
        [3, 0, 0.42],
        [3, 1, -0.61],
        [3, 2, 0.55],
        [3, 3, 1],
      ],
      label: {
        show: true,
        color: '#222',
        fontWeight: 'bold',
        formatter: function (params) {
          return params.value[2].toFixed(2)
        },
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowColor: 'rgba(0, 0, 0, 0.5)',
        },
      },
    },
  ],
})

const downloadReport = () => {
  // Select the main report container
  const main = document.querySelector('.resultview-main')
  if (!main) return

  // Helper: replace ECharts with images
  const chartClasses = ['trend-echart', 'abnormal-echart', 'dimension-echart', 'corr-heatmap']
  const replaced = [] // {el, img, parent, next}

  chartClasses.forEach((cls) => {
    const chartEl = main.querySelector(`.${cls}`)
    if (chartEl) {
      console.log(`[Export] Found chart element for class .${cls}:`, chartEl)
      // Try to get the Vue component instance
      let vueComp = null
      if (chartEl.__vueParentComponent && chartEl.__vueParentComponent.proxy) {
        vueComp = chartEl.__vueParentComponent.proxy
        console.log(`[Export] Found vue component via __vueParentComponent.proxy:`, vueComp)
      } else if (chartEl.__vue__) {
        vueComp = chartEl.__vue__
        console.log(`[Export] Found vue component via __vue__`, vueComp)
      }
      let echartsInstance = null
      if (vueComp) {
        if (typeof vueComp.getEchartsInstance === 'function') {
          echartsInstance = vueComp.getEchartsInstance()
          console.log('[Export] Got ECharts instance from getEchartsInstance()')
        } else if (
          vueComp.echartsRef &&
          typeof vueComp.echartsRef.getEchartsInstance === 'function'
        ) {
          echartsInstance = vueComp.echartsRef.getEchartsInstance()
          console.log('[Export] Got ECharts instance from echartsRef.getEchartsInstance()')
        } else if (
          vueComp.$refs &&
          vueComp.$refs.echartsRef &&
          typeof vueComp.$refs.echartsRef.getEchartsInstance === 'function'
        ) {
          echartsInstance = vueComp.$refs.echartsRef.getEchartsInstance()
          console.log('[Export] Got ECharts instance from $refs.echartsRef.getEchartsInstance()')
        } else if (vueComp.chart) {
          echartsInstance = vueComp.chart
          console.log('[Export] Got ECharts instance from .chart')
        } else {
          console.warn('[Export] No ECharts instance found on vueComp', vueComp)
        }
      }
      if (echartsInstance) {
        const dataUrl = echartsInstance.getDataURL({ pixelRatio: 2, backgroundColor: '#fff' })
        const img = document.createElement('img')
        img.src = dataUrl
        img.className = cls
        img.style.cssText =
          chartEl.style.cssText + 'max-width:500px;height:auto;display:block;margin:0 auto;'
        // Replace chart with image
        const parent = chartEl.parentNode
        const next = chartEl.nextSibling
        parent.replaceChild(img, chartEl)
        replaced.push({ el: chartEl, img, parent, next })
        console.log(`[Export] Chart replaced with image for .${cls}`)
      } else {
        console.warn(`[Export] Could not find ECharts instance for .${cls}`)
      }
    } else {
      console.warn(`[Export] Could not find element for .${cls}`)
    }
  })

  // Get the styles from the current document
  const styles = Array.from(document.querySelectorAll('style, link[rel="stylesheet"]'))
    .map((node) => node.outerHTML)
    .join('\n')

  // Add chart image size CSS
  const chartImgCSS = `\n<style>\n.trend-echart, .abnormal-echart, .dimension-echart, .corr-heatmap { max-width:500px; height:auto; display:block; margin:0 auto; }\n</style>`

  // Build a minimal HTML document
  const html = `<!DOCTYPE html>
<html lang=\"zh-CN\">
<head>
  <meta charset=\"UTF-8\">
  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">
  <title>个贷催收场景分析 - 结果报告</title>
  ${styles}
  ${chartImgCSS}
  <style>
    body { background: #f5f8fc; margin: 0; }
    .resultview-main { margin: 36px auto; }
  </style>
</head>
<body>
  <div class=\"resultview-root\" style=\"width:100vw;min-height:100vh;display:flex;flex-direction:column;align-items:center;background:#f5f8fc;\">
    ${main.outerHTML}
  </div>
</body>
</html>`

  // Restore original charts
  replaced.forEach(({ el, img, parent, next }) => {
    parent.replaceChild(el, img)
  })

  // Create a Blob and trigger download
  const blob = new Blob([html], { type: 'text/html' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = '个贷催收场景分析-结果报告.html'
  document.body.appendChild(a)
  a.click()
  setTimeout(() => {
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }, 100)
}
</script>

<style scoped>
.resultview-root {
  width: 100vw;
  min-height: calc(100vh - 120px);
  background: #f5f8fc;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.resultview-main {
  width: 100%;
  max-width: 1100px;
  background: #fff;
  border-radius: 18px;
  box-shadow: 0 4px 24px 0 rgba(0, 32, 72, 0.08);
  margin-top: 36px;
  margin-bottom: 36px;
  padding: 48px 48px 32px 48px;
  display: flex;
  flex-direction: column;
  gap: 36px;
}
.resultview-header {
  border-bottom: 1.5px solid #e0e6f0;
  margin-bottom: 24px;
  padding-bottom: 18px;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.resultview-title {
  font-size: 2.1rem;
  font-weight: bold;
  color: #1976d2;
  margin: 0 0 8px 0;
}
.resultview-meta {
  color: #888;
  font-size: 1rem;
  display: flex;
  gap: 32px;
}
.section-title {
  font-size: 1.18rem;
  color: #1976d2;
  font-weight: 600;
  margin-bottom: 16px;
  letter-spacing: 1px;
}
.ai-summary-box {
  background: #f7faff;
  border-left: 5px solid #1976d2;
  border-radius: 10px;
  padding: 22px 28px 18px 28px;
  margin-bottom: 8px;
}
.ai-summary-title {
  font-size: 1.08rem;
  font-weight: 600;
  color: #1976d2;
  margin-bottom: 10px;
}
.ai-summary-content {
  color: #222;
  font-size: 1.02rem;
  line-height: 1.8;
}
.trend-summary-row {
  display: flex;
  gap: 32px;
  margin-bottom: 18px;
}
.trend-summary-card {
  background: #f5f8fc;
  border-radius: 10px;
  box-shadow: 0 2px 8px 0 rgba(25, 118, 210, 0.06);
  padding: 18px 32px 14px 32px;
  min-width: 160px;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.trend-label {
  color: #1976d2;
  font-size: 1.02rem;
  font-weight: 500;
  margin-bottom: 8px;
}
.trend-value {
  font-size: 1.5rem;
  font-weight: bold;
  color: #222;
  margin-bottom: 4px;
}
.trend-desc {
  color: #42b983;
  font-size: 0.98rem;
}
.trend-chart-section {
  margin-top: 8px;
  text-align: center;
}
.trend-echart {
  width: 100%;
  max-width: 950px;
  height: 400px;
  margin: 0 auto;
  border-radius: 14px;
  box-shadow: 0 2px 8px 0 rgba(25, 118, 210, 0.06);
  background: #f7faff;
}
.trend-chart-caption {
  color: #888;
  font-size: 0.98rem;
  margin-top: 6px;
}
.abnormal-box {
  background: #fff8f6;
  border-left: 5px solid #e53935;
  border-radius: 10px;
  padding: 22px 28px 18px 28px;
  margin-bottom: 8px;
  box-shadow: 0 2px 8px 0 rgba(229, 57, 53, 0.06);
}
.abnormal-title {
  font-size: 1.08rem;
  font-weight: 600;
  color: #e53935;
  margin-bottom: 10px;
}
.abnormal-content {
  color: #b71c1c;
  font-size: 1.02rem;
  line-height: 1.8;
}
.abnormal-echart {
  width: 100%;
  max-width: 700px;
  height: 320px;
  margin: 28px auto 0 auto;
  border-radius: 14px;
  background: #fff;
}
.dimension-box {
  background: #f7faff;
  border-left: 5px solid #42b983;
  border-radius: 10px;
  padding: 22px 28px 18px 28px;
  margin-bottom: 8px;
  box-shadow: 0 2px 8px 0 rgba(66, 185, 131, 0.06);
}
.dimension-title {
  font-size: 1.08rem;
  font-weight: 600;
  color: #42b983;
  margin-bottom: 10px;
}
.dimension-content {
  color: #1976d2;
  font-size: 1.02rem;
  line-height: 1.8;
}
.dimension-echart {
  width: 100%;
  max-width: 700px;
  height: 320px;
  margin: 28px auto 0 auto;
  border-radius: 14px;
  background: #fff;
}
.corr-grid {
  display: flex;
  gap: 24px;
  margin-bottom: 18px;
}
.corr-card {
  background: #f5f8fc;
  border-radius: 10px;
  box-shadow: 0 2px 8px 0 rgba(25, 118, 210, 0.06);
  padding: 18px 24px 14px 24px;
  min-width: 180px;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.corr-label {
  color: #1976d2;
  font-size: 1.02rem;
  font-weight: 500;
  margin-bottom: 8px;
}
.corr-value {
  font-size: 1.18rem;
  font-weight: bold;
  color: #222;
  margin-bottom: 4px;
}
.corr-desc {
  color: #888;
  font-size: 0.98rem;
}
.corr-chart-section {
  margin-top: 8px;
  text-align: center;
}
.datasheet-table-wrapper {
  margin-top: 8px;
  overflow-x: auto;
}
.datasheet-table {
  width: 100%;
  border-collapse: collapse;
  background: #f7faff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}
.datasheet-table th {
  padding: 12px 16px;
  text-align: center;
  border-bottom: 1px solid #e0e0e0;
  font-size: 1rem;
  background: #eaf3ff;
  color: #1976d2;
  font-weight: 600;
}
.datasheet-table td {
  padding: 12px 16px;
  text-align: center;
  border-bottom: 1px solid #e0e0e0;
  font-size: 1rem;
  color: #333;
}
.datasheet-table tr:last-child td {
  border-bottom: none;
}
.datasheet-caption {
  color: #888;
  font-size: 0.98rem;
  margin-top: 6px;
}
.advice-box {
  background: #f7faff;
  border-left: 5px solid #42b983;
  border-radius: 10px;
  padding: 18px 28px 14px 28px;
  color: #222;
  font-size: 1.02rem;
  line-height: 1.8;
}
.corr-heatmap {
  width: 100%;
  max-width: 800px;
  height: 340px;
  margin: 0 auto;
  border-radius: 14px;
  background: #f7faff;
  box-shadow: 0 2px 8px 0 rgba(25, 118, 210, 0.06);
}
.download-btn {
  position: absolute;
  top: 18px;
  right: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  background: #1976d2;
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 8px 18px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  box-shadow: 0 2px 8px 0 rgba(25, 118, 210, 0.08);
  transition:
    background 0.2s,
    color 0.2s;
  z-index: 2;
}
.download-btn:hover {
  background: #1251a3;
  color: #fff;
}
.download-btn svg {
  margin-right: 2px;
}
@media (max-width: 1100px) {
  .trend-echart,
  .corr-heatmap,
  .abnormal-echart,
  .dimension-echart {
    max-width: 98vw;
    height: 240px;
  }
}
@media (max-width: 700px) {
  .trend-echart,
  .corr-heatmap,
  .abnormal-echart,
  .dimension-echart {
    max-width: 100vw;
    height: 180px;
    border-radius: 8px;
  }
  .download-btn {
    position: static;
    margin-top: 12px;
    width: 100%;
    justify-content: center;
  }
  .resultview-header {
    align-items: stretch;
  }
}
</style>
