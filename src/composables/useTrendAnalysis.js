import { computed } from 'vue'
import { storeToRefs } from 'pinia'
import { useStates } from '../store/states'
import { request } from '../utils/request'

export function useTrendAnalysis() {
  const statesStore = useStates()
  const { updateComponent } = statesStore
  const { states } = storeToRefs(statesStore)

  // Get the trend analysis component from store
  const trendComponent = computed(() =>
    states.value.find((state) => state.componentName === 'trendAnalysis'),
  )

  // Get available indicators and dimensions
  const availableIndicators = computed(() => {
    const indicatorComponent = states.value.find((state) => state.componentName === '目标指标')
    if (!indicatorComponent) return []

    const indices = indicatorComponent.indices || []
    return indices
      .map((index) => {
        // Handle both old and new data formats
        if (typeof index === 'object' && index.indexName) {
          return index.indexName
        }
        return typeof index === 'string' ? index : index.indexName
      })
      .filter((name) => name)
  })

  const availableDimensions = computed(() => {
    const dimensionComponent = states.value.find((state) => state.componentName === '目标维度')
    if (!dimensionComponent) return []

    const dimensions = dimensionComponent.dimensions || []
    return dimensions
      .map((dimension) => {
        // Handle both old and new data formats
        if (typeof dimension === 'object' && dimension.dimensionName) {
          return dimension.dimensionName
        }
        return typeof dimension === 'string' ? dimension : dimension.dimensionName
      })
      .filter((name) => name)
  })

  // Sync selectedTrends with store
  const selectedTrends = computed({
    get: () => trendComponent.value?.selectedTrends || [],
    set: (newValue) => {
      if (trendComponent.value) {
        updateComponent(trendComponent.value.componentId, {
          ...trendComponent.value,
          selectedTrends: newValue,
        })
      }
    },
  })

  // Run analysis
  const runAnalysis = async () => {
    if (!trendComponent.value) {
      throw new Error('Trend analysis component not found')
    }

    const requestBody = {
      states: states.value,
    }

    const response = await request(
      `/api/compute?component_id=${trendComponent.value.componentId}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      },
    )

    if (response.error) {
      throw new Error(response.error)
    }

    // Update the component with the computation results
    updateComponent(trendComponent.value.componentId, {
      ...trendComponent.value,
      results: response,
    })
    console.log('Analysis completed:', response)
    return response
  }

  return {
    trendComponent,
    availableIndicators,
    availableDimensions,
    selectedTrends,
    runAnalysis,
  }
}
