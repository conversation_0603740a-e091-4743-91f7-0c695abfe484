import { useStates } from '@/store/states'
import * as XLSX from 'xlsx'
import Papa from 'papaparse'

export async function importAndAddSheet(file) {
  const statesStore = useStates()
  const tableState = statesStore.states.find((s) => s.componentName === 'table')
  if (!tableState) return

  let columns = []
  let rows = []
  if (file.name.endsWith('.csv')) {
    // Parse CSV
    const text = await file.text()
    const result = Papa.parse(text, { header: true })
    columns = result.meta.fields.map((field) => ({ field, title: field }))
    rows = result.data.filter((row) => Object.values(row).some((v) => v !== undefined && v !== ''))
  } else {
    // Parse Excel
    const data = await file.arrayBuffer()
    const workbook = XLSX.read(data, { type: 'array' })
    const wsname = workbook.SheetNames[0]
    const ws = workbook.Sheets[wsname]
    const json = XLSX.utils.sheet_to_json(ws, { header: 1 })
    if (json.length > 0) {
      columns = json[0].map((field) => ({ field, title: field }))
      rows = json.slice(1).map((row) => {
        const obj = {}
        columns.forEach((col, i) => {
          obj[col.field] = row[i]
        })
        return obj
      })
    }
  }
  if (!columns.length || !rows.length) return
  const idx = Object.keys(tableState.sheetData || {}).length + 1
  const key = `sheet${Date.now()}`
  const newSheetData = { ...(tableState.sheetData || {}) }
  newSheetData[key] = {
    name: file.name.replace(/\.[^.]+$/, ''),
    columns,
    rows,
  }
  statesStore.updateComponent(tableState.componentId, {
    ...tableState,
    sheetData: newSheetData,
    activeSheet: key,
  })
}
