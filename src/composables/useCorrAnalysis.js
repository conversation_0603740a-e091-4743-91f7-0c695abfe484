import { computed } from 'vue'
import { storeToRefs } from 'pinia'
import { useStates } from '../store/states'
import { request } from '../utils/request'

export function useCorrAnalysis() {
  const statesStore = useStates()
  const { updateComponent } = statesStore
  const { states } = storeToRefs(statesStore)

  // Get the correlation analysis component from store
  const corrComponent = computed(() =>
    states.value.find((state) => state.componentName === 'corrAnalysis'),
  )

  // Get available indicators and dimensions
  const availableIndicators = computed(() => {
    const indicatorComponent = states.value.find((state) => state.componentName === '目标指标')
    if (!indicatorComponent) return []

    const indices = indicatorComponent.indices || []
    return indices
      .map((index) => {
        // Handle both old and new data formats
        if (typeof index === 'object' && index.indexName) {
          return index.indexName
        }
        return typeof index === 'string' ? index : index.indexName
      })
      .filter((name) => name)
  })

  const availableDimensions = computed(() => {
    const dimensionComponent = states.value.find((state) => state.componentName === '目标维度')
    if (!dimensionComponent) return []

    const dimensions = dimensionComponent.dimensions || []
    return dimensions
      .map((dimension) => {
        // Handle both old and new data formats
        if (typeof dimension === 'object' && dimension.dimensionName) {
          return dimension.dimensionName
        }
        return typeof dimension === 'string' ? dimension : dimension.dimensionName
      })
      .filter((name) => name)
  })

  // Combine indicators and dimensions for selection
  const availableObjects = computed(() => {
    const indicators = availableIndicators.value.map((indicator) => ({
      value: indicator,
      label: indicator,
      type: 'indicator',
    }))

    const dimensions = availableDimensions.value.map((dimension) => ({
      value: dimension,
      label: dimension,
      type: 'dimension',
    }))

    return [...indicators, ...dimensions]
  })

  // Sync selectedObjects with store
  const selectedObjects = computed({
    get: () => corrComponent.value?.selectedObjects || [],
    set: (newValue) => {
      if (corrComponent.value) {
        updateComponent(corrComponent.value.componentId, {
          ...corrComponent.value,
          selectedObjects: newValue,
        })
      }
    },
  })

  // Run analysis
  const runAnalysis = async () => {
    if (!corrComponent.value) {
      throw new Error('Correlation analysis component not found')
    }

    if (selectedObjects.value.length !== 2) {
      throw new Error('Please select exactly 2 objects for correlation analysis')
    }

    const requestBody = {
      states: states.value,
    }

    const response = await request(`/api/compute?component_id=${corrComponent.value.componentId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
    })

    if (response.error) {
      throw new Error(response.error)
    }

    // Update the component with the computation results
    updateComponent(corrComponent.value.componentId, {
      ...corrComponent.value,
      results: response,
    })
    console.log('Correlation analysis completed:', response)
    return response
  }

  return {
    corrComponent,
    availableIndicators,
    availableDimensions,
    availableObjects,
    selectedObjects,
    runAnalysis,
  }
}
