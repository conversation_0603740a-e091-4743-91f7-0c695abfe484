import axios from 'axios'
import { ref, watch } from 'vue'
import { useStates } from '@/store/states'

export function useChatFlow() {
  const statesStore = useStates()
  // Use Pinia store for chat log
  const messages = ref(statesStore.chatMessages.slice())
  const inputMessage = ref('')
  const loading = ref(false)

  // Keep store and local messages in sync
  watch(
    messages,
    (val) => {
      statesStore.setChatMessages(val)
    },
    { deep: true },
  )

  // Helper to convert local messages to Ollama format
  function toOllamaMessages(msgs, systemPrompt) {
    const ollamaMsgs = []
    if (systemPrompt) {
      ollamaMsgs.push({ role: 'system', content: systemPrompt })
    }
    ollamaMsgs.push(
      ...msgs.map((m) => ({
        role: m.type === 'user' ? 'user' : m.type === 'server' ? 'assistant' : 'user',
        content: m.content,
      })),
    )
    return ollamaMsgs
  }

  // Collect background info from states (RAG)
  function getBackgroundInfo() {
    const states = statesStore.states
    const indicesComp = states.find((s) => s.componentName === '目标指标')
    const dimensionsComp = states.find((s) => s.componentName === '目标维度')
    const tableComp = states.find((s) => s.componentName === 'table')
    const indices = indicesComp?.indices || []
    const dimensions = dimensionsComp?.dimensions || []
    let tableSummary = ''
    if (tableComp && tableComp.sheetData && Object.keys(tableComp.sheetData).length > 0) {
      // Only include the active sheet
      const activeSheet = tableComp.activeSheet
      const sheet = tableComp.sheetData[activeSheet]
      if (sheet && sheet.columns && sheet.rows) {
        // Only include up to 5 rows for context
        const previewRows = sheet.rows.slice(0, 5)
        tableSummary =
          `表头: ${sheet.columns.map((c) => c.title || c.field).join(', ')}\n` +
          previewRows
            .map((row, i) => `第${i + 1}行: ` + sheet.columns.map((c) => row[c.field]).join(', '))
            .join('\n')
      }
    }
    let info = ''
    if (indices.length > 0) info += `当前分析指标: ${indices.join(', ')}\n`
    if (dimensions.length > 0) info += `当前分析维度: ${dimensions.join(', ')}\n`
    if (tableSummary) info += `数据预览:\n${tableSummary}`
    return info.trim()
  }

  const sendMessage = async () => {
    if (!inputMessage.value.trim()) return

    const msgObj = {
      type: 'user',
      content: inputMessage.value,
    }
    messages.value.push(msgObj)
    inputMessage.value = ''
    loading.value = true
    try {
      // Collect background info for RAG
      const backgroundInfo = getBackgroundInfo()
      const ollamaMessages = toOllamaMessages(messages.value, backgroundInfo)
      const response = await axios.post('http://localhost:11434/api/chat', {
        model: 'deepseek-r1:7b',
        messages: ollamaMessages,
        stream: false,
      })
      const reply = response.data?.message?.content || ''
      messages.value.push({
        type: 'server',
        content: reply,
      })
      console.log(response)
      return response.data
    } catch (error) {
      console.error('发送消息失败:', error)
      messages.value.push({
        type: 'error',
        content: '发送消息失败，请重试',
      })
    } finally {
      loading.value = false
    }
  }

  // Refresh/clear chat log
  function refreshChat() {
    messages.value = []
    statesStore.clearChatMessages()
  }

  return {
    messages,
    inputMessage,
    sendMessage,
    loading,
    refreshChat,
  }
}
