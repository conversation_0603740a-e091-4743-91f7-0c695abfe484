<template>
  <div class="custom-select" @click="toggleDropdown" ref="selectContainer">
    <div class="select-display">
      <span>
        <template v-if="selectedValues.length === 0"> 请选择 </template>
        <template v-else>
          <template v-for="(item, idx) in displayText.shown" :key="item">
            <span>{{ item }}</span
            ><span v-if="idx < displayText.shown.length - 1">, </span>
          </template>
          <span v-if="displayText.hiddenCount > 0" class="plus-n"
            >+{{ displayText.hiddenCount }}</span
          >
        </template>
        <button
          v-if="!expandedDisplay && selectedValues.length > maxDisplay"
          class="expand-btn"
          @click.stop="expandedDisplay = true"
        >
          展开
        </button>
        <button
          v-if="expandedDisplay && selectedValues.length > maxDisplay"
          class="expand-btn"
          @click.stop="expandedDisplay = false"
        >
          收起
        </button>
      </span>
      <span class="select-arrow">▼</span>
    </div>
    <div v-if="isOpen" class="select-dropdown">
      <div v-for="(group, index) in groups" :key="index" class="select-group">
        <div class="group-label">
          {{ group.label }}
          <label style="float: right; font-weight: normal; cursor: pointer">
            <input
              type="checkbox"
              :checked="isGroupAllSelected(group)"
              @change="toggleGroupSelectAll(group, $event)"
              @click.stop
            />
            <span style="font-size: 0.95em">全选</span>
          </label>
        </div>
        <label v-for="option in group.options" :key="option.value" class="select-option">
          <input type="checkbox" :value="option.value" v-model="selectedValues" @click.stop />
          <span>{{ option.label }}</span>
        </label>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'

const props = defineProps({
  groups: {
    type: Array,
    required: true,
    // Each group should have { label: string, options: Array<{value: any, label: string}> }
  },
  modelValue: {
    type: Array,
    default: () => [],
  },
  maxDisplay: {
    type: Number,
    default: 3,
  },
})

const emit = defineEmits(['update:modelValue'])

const isOpen = ref(false)
const selectContainer = ref(null)
const selectedValues = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

const expandedDisplay = ref(false)

const displayText = computed(() => {
  if (selectedValues.value.length === 0) {
    return { shown: [], hiddenCount: 0 }
  }
  if (expandedDisplay.value || selectedValues.value.length <= props.maxDisplay) {
    return { shown: selectedValues.value, hiddenCount: 0 }
  }
  const shown = selectedValues.value.slice(0, props.maxDisplay)
  const hiddenCount = selectedValues.value.length - props.maxDisplay
  return { shown, hiddenCount }
})

const toggleDropdown = (event) => {
  // If click is on expand/collapse, don't toggle dropdown
  if (event && event.target && event.target.classList.contains('expand-btn')) return
  isOpen.value = !isOpen.value
}

const handleClickOutside = (event) => {
  if (selectContainer.value && !selectContainer.value.contains(event.target)) {
    isOpen.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

// Helper: check if all options in a group are selected
function isGroupAllSelected(group) {
  if (!group.options.length) return false
  return group.options.every((opt) => selectedValues.value.includes(opt.value))
}

// Helper: toggle select all for a group
function toggleGroupSelectAll(group, event) {
  const groupValues = group.options.map((opt) => opt.value)
  if (event.target.checked) {
    // Add all group values to selectedValues (avoid duplicates)
    const newValues = Array.from(new Set([...selectedValues.value, ...groupValues]))
    emit('update:modelValue', newValues)
  } else {
    // Remove all group values from selectedValues
    const newValues = selectedValues.value.filter((val) => !groupValues.includes(val))
    emit('update:modelValue', newValues)
  }
}
</script>

<style scoped>
.custom-select {
  width: 100%;
  position: relative;
  cursor: pointer;
}

.select-display {
  width: 100%;
  padding: 0.5rem 1rem;
  border: 1px solid #bbb;
  border-radius: 20px;
  font-size: 1rem;
  background: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #666;
  min-height: 2.2rem;
  position: relative;
}

.select-display .expand-btn {
  color: #4285f4;
  font-weight: 500;
  background: none;
  border: none;
  font-size: 0.95rem;
  margin-left: 0.5rem;
  cursor: pointer;
  padding: 0 0.2rem;
  border-radius: 6px;
  transition: background 0.2s;
}

.select-display .expand-btn:hover {
  background: #e3f2fd;
}

.select-arrow {
  font-size: 0.8rem;
  color: #666;
  transition: transform 0.2s;
}

.custom-select:hover .select-display {
  border-color: #4285f4;
}

.select-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  margin-top: 0.5rem;
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  max-height: 300px;
  overflow-y: auto;
}

.select-group {
  padding: 0.5rem 0;
}

.select-group:not(:last-child) {
  border-bottom: 1px solid #eee;
}

.group-label {
  padding: 0.5rem 1rem;
  font-weight: bold;
  color: #1976d2;
  background: #f5f5f5;
}

.select-option {
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.select-option:hover {
  background-color: #f5f5f5;
}

.select-option input[type='checkbox'] {
  margin-right: 0.5rem;
}

.select-option span {
  color: #333;
}

/* Scrollbar styling */
.select-dropdown::-webkit-scrollbar {
  width: 8px;
}

.select-dropdown::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.select-dropdown::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 4px;
}

.select-dropdown::-webkit-scrollbar-thumb:hover {
  background: #999;
}

.select-display .plus-n {
  color: #4285f4;
  font-weight: 500;
  margin-left: 0.2em;
}
</style>
