<template>
  <div v-if="show" class="system-error-mask" @click.self="handleMaskClick">
    <div class="error-dialog">
      <!-- Error Icon -->
      <div class="error-icon">
        <svg
          width="64"
          height="64"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <circle cx="12" cy="12" r="10" stroke="#ff4757" stroke-width="2" fill="#fff2f2" />
          <path d="M15 9l-6 6" stroke="#ff4757" stroke-width="2" stroke-linecap="round" />
          <path d="M9 9l6 6" stroke="#ff4757" stroke-width="2" stroke-linecap="round" />
        </svg>
      </div>

      <!-- Error Title -->
      <h2 class="error-title">{{ title || '系统错误' }}</h2>

      <!-- Error Message -->
      <div class="error-message">
        <p>{{ message || '系统遇到了一个错误，请稍后重试。' }}</p>
        <div v-if="errorCode" class="error-code">错误代码: {{ errorCode }}</div>
        <div class="error-timestamp">时间: {{ formatTimestamp(timestamp) }}</div>
      </div>

      <!-- Error Details (expandable) -->
      <div class="error-details">
        <button
          class="details-toggle"
          @click="showDetails = !showDetails"
          :class="{ expanded: showDetails }"
        >
          {{ showDetails ? '隐藏详情' : '查看详情' }}
          <span class="toggle-icon">{{ showDetails ? '▲' : '▼' }}</span>
        </button>

        <div v-if="showDetails" class="details-content">
          <div class="detail-item"><strong>错误类型:</strong> 系统级错误</div>
          <div class="detail-item"><strong>影响范围:</strong> 当前操作无法完成</div>
          <div class="detail-item"><strong>建议操作:</strong> 请尝试刷新页面或稍后重试</div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="error-actions">
        <button v-if="retryAction" class="retry-btn" @click="handleRetry" :disabled="retrying">
          <span v-if="retrying" class="loading-spinner"></span>
          {{ retrying ? '重试中...' : '重试' }}
        </button>

        <button class="refresh-btn" @click="handleRefresh">刷新页面</button>

        <button class="contact-btn" @click="handleContactSupport">联系技术支持</button>

        <button class="dismiss-btn" @click="handleDismiss">关闭</button>
      </div>

      <!-- Help Text -->
      <div class="help-text">
        <p>如果问题持续存在，请联系技术支持团队。</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useStates } from '@/store/states'

const statesStore = useStates()

// Props from store
const show = computed(() => statesStore.systemError.show)
const title = computed(() => statesStore.systemError.title)
const message = computed(() => statesStore.systemError.message)
const errorCode = computed(() => statesStore.systemError.errorCode)
const retryAction = computed(() => statesStore.systemError.retryAction)
const timestamp = computed(() => statesStore.systemError.timestamp)

// Local state
const showDetails = ref(false)
const retrying = ref(false)

// Format timestamp
const formatTimestamp = (ts) => {
  if (!ts) return ''
  return new Date(ts).toLocaleString('zh-CN')
}

// Handle retry action
const handleRetry = async () => {
  if (!retryAction.value || retrying.value) return

  try {
    retrying.value = true
    await retryAction.value()
    // If retry succeeds, hide the error mask
    statesStore.hideSystemError()
  } catch (error) {
    console.error('Retry failed:', error)
    // Keep the mask open if retry fails
  } finally {
    retrying.value = false
  }
}

// Handle refresh page
const handleRefresh = () => {
  window.location.reload()
}

// Handle contact support
const handleContactSupport = () => {
  // You can implement this to open a support ticket system
  // For now, we'll show an alert with contact information
  alert('请联系技术支持:\n邮箱: <EMAIL>\n电话: 400-xxx-xxxx')
}

// Handle dismiss
const handleDismiss = () => {
  statesStore.hideSystemError()
}

// Handle clicking on mask background
const handleMaskClick = () => {
  // Prevent dismissing on background click for system errors
  // Users should explicitly choose an action
}
</script>

<style scoped>
.system-error-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  backdrop-filter: blur(4px);
}

.error-dialog {
  background: white;
  border-radius: 16px;
  padding: 32px;
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  text-align: center;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.error-icon {
  margin-bottom: 24px;
}

.error-title {
  color: #ff4757;
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 16px 0;
}

.error-message {
  margin-bottom: 24px;
  text-align: left;
}

.error-message p {
  color: #333;
  font-size: 16px;
  line-height: 1.5;
  margin: 0 0 12px 0;
}

.error-code {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 8px 12px;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  color: #666;
  margin: 8px 0;
}

.error-timestamp {
  font-size: 12px;
  color: #999;
  margin-top: 8px;
}

.error-details {
  color: #333;
  margin-bottom: 24px;
  text-align: left;
}

.details-toggle {
  background: none;
  border: none;
  color: #007bff;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 0;
  width: 100%;
  justify-content: center;
}

.details-toggle:hover {
  color: #0056b3;
}

.toggle-icon {
  transition: transform 0.2s ease;
}

.details-toggle.expanded .toggle-icon {
  transform: rotate(180deg);
}

.details-content {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  margin-top: 12px;
}

.detail-item {
  margin-bottom: 8px;
  font-size: 14px;
  line-height: 1.4;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.error-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 24px;
}

.error-actions button {
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.retry-btn {
  background: #28a745;
  color: white;
}

.retry-btn:hover:not(:disabled) {
  background: #218838;
}

.retry-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.refresh-btn {
  background: #007bff;
  color: white;
}

.refresh-btn:hover {
  background: #0056b3;
}

.contact-btn {
  background: #ffc107;
  color: #212529;
}

.contact-btn:hover {
  background: #e0a800;
}

.dismiss-btn {
  background: #6c757d;
  color: white;
}

.dismiss-btn:hover {
  background: #545b62;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.help-text {
  border-top: 1px solid #e9ecef;
  padding-top: 16px;
  text-align: center;
}

.help-text p {
  color: #666;
  font-size: 12px;
  margin: 0;
}

/* Responsive design */
@media (max-width: 600px) {
  .error-dialog {
    padding: 24px;
    margin: 16px;
  }

  .error-actions {
    gap: 8px;
  }

  .error-actions button {
    padding: 10px 16px;
    font-size: 13px;
  }
}
</style>
