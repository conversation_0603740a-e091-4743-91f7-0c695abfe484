<template>
  <div v-if="show" class="notification-toast" :class="type">
    <span class="notification-icon">
      {{ type === 'success' ? '✅' : '❌' }}
    </span>
    <span class="notification-message">{{ message }}</span>
  </div>
</template>

<script setup>
import { watch, onMounted, onBeforeUnmount } from 'vue'
const props = defineProps({
  show: <PERSON><PERSON><PERSON>,
  message: String,
  type: {
    type: String,
    default: 'success', // 'success' | 'error'
  },
  duration: {
    type: Number,
    default: 10000,
  },
})
const emit = defineEmits(['close'])
let timer = null

watch(
  () => props.show,
  (val) => {
    if (val) {
      clearTimeout(timer)
      timer = setTimeout(() => {
        emit('close')
      }, props.duration)
    } else {
      clearTimeout(timer)
    }
  },
)
onMounted(() => {
  if (props.show) {
    timer = setTimeout(() => {
      emit('close')
    }, props.duration)
  }
})
onBeforeUnmount(() => {
  clearTimeout(timer)
})
</script>

<style scoped>
.notification-toast {
  position: fixed;
  top: 20px;
  right: 20px;
  background-color: #fff;
  border-radius: 8px;
  padding: 12px 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  gap: 8px;
  z-index: 1000;
  border-left: 4px solid #4caf50;
  animation: slideIn 0.3s ease;
}
@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}
@keyframes slideOut {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}
.notification-icon {
  font-size: 18px;
}
.notification-message {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}
.notification-toast.success {
  border-left-color: #4caf50;
}
.notification-toast.error {
  border-left-color: #f44336;
}
</style>
