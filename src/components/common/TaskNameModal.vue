<template>
  <div v-if="show" class="task-name-mask" @click.self="handleMaskClick">
    <div class="task-name-dialog">
      <!-- Header -->
      <div class="dialog-header">
        <h3 class="dialog-title">任务命名</h3>
        <button class="close-btn" @click="handleCancel" title="关闭">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
            <path
              d="M18 6L6 18M6 6l12 12"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </button>
      </div>

      <!-- Content -->
      <div class="dialog-content">
        <p class="dialog-description">请为当前任务设置一个名称，便于后续查找和管理。</p>

        <div class="input-group">
          <label for="taskNameInput" class="input-label">任务名称</label>
          <input
            id="taskNameInput"
            ref="taskNameInput"
            v-model="inputValue"
            type="text"
            class="task-name-input"
            :class="{ 'input-error': hasError }"
            placeholder="请输入任务名称"
            maxlength="50"
            @keyup.enter="handleConfirm"
            @input="clearError"
          />
          <div v-if="hasError" class="error-message">{{ errorMessage }}</div>
          <div class="input-hint">最多50个字符</div>
        </div>
      </div>

      <!-- Actions -->
      <div class="dialog-actions">
        <button class="cancel-btn" @click="handleCancel">取消</button>
        <button class="confirm-btn" @click="handleConfirm" :disabled="isConfirming">
          <span v-if="isConfirming" class="loading-spinner"></span>
          {{ isConfirming ? '保存中...' : '确认' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, nextTick } from 'vue'

const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  initialValue: {
    type: String,
    default: '个贷催收场景经营分析',
  },
})

const emit = defineEmits(['confirm', 'cancel', 'close'])

// Local state
const inputValue = ref('')
const hasError = ref(false)
const errorMessage = ref('')
const isConfirming = ref(false)
const taskNameInput = ref(null)

// Watch for show prop changes to reset state and focus input
watch(
  () => props.show,
  (newShow) => {
    if (newShow) {
      inputValue.value = props.initialValue
      hasError.value = false
      errorMessage.value = ''
      isConfirming.value = false

      // Focus input after modal is shown
      nextTick(() => {
        if (taskNameInput.value) {
          taskNameInput.value.focus()
          taskNameInput.value.select()
        }
      })

      // Add escape key listener
      document.addEventListener('keydown', handleEscapeKey)
    } else {
      // Remove escape key listener
      document.removeEventListener('keydown', handleEscapeKey)
    }
  },
)

// Handle escape key
const handleEscapeKey = (event) => {
  if (event.key === 'Escape') {
    handleCancel()
  }
}

// Validation function
const validateInput = () => {
  const trimmedValue = inputValue.value.trim()

  if (!trimmedValue) {
    hasError.value = true
    errorMessage.value = '任务名称不能为空'
    return false
  }

  if (trimmedValue.length > 50) {
    hasError.value = true
    errorMessage.value = '任务名称不能超过50个字符'
    return false
  }

  return true
}

// Clear error state
const clearError = () => {
  hasError.value = false
  errorMessage.value = ''
}

// Handle confirm action
const handleConfirm = async () => {
  if (!validateInput()) {
    return
  }

  isConfirming.value = true

  try {
    const trimmedValue = inputValue.value.trim()
    emit('confirm', trimmedValue)
  } catch (error) {
    console.error('Error confirming task name:', error)
    hasError.value = true
    errorMessage.value = '保存失败，请重试'
  } finally {
    isConfirming.value = false
  }
}

// Handle cancel action
const handleCancel = () => {
  emit('cancel')
}

// Handle mask click (background click)
const handleMaskClick = () => {
  // Allow dismissing on background click
  handleCancel()
}
</script>

<style scoped>
.task-name-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9998;
  backdrop-filter: blur(3px);
}

.task-name-dialog {
  background: white;
  border-radius: 12px;
  padding: 0;
  max-width: 480px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.2);
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.dialog-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
}

.close-btn {
  background: none;
  border: none;
  padding: 4px;
  cursor: pointer;
  color: #6b7280;
  border-radius: 4px;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

.dialog-content {
  padding: 24px;
}

.dialog-description {
  margin: 0 0 20px 0;
  color: #6b7280;
  font-size: 0.95rem;
  line-height: 1.5;
}

.input-group {
  margin-bottom: 8px;
}

.input-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #374151;
  font-size: 0.95rem;
}

.task-name-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #d1d5db;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.2s;
  outline: none;
  box-sizing: border-box;
}

.task-name-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.task-name-input.input-error {
  border-color: #ef4444;
}

.task-name-input.input-error:focus {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.error-message {
  margin-top: 6px;
  color: #ef4444;
  font-size: 0.875rem;
}

.input-hint {
  margin-top: 6px;
  color: #9ca3af;
  font-size: 0.875rem;
}

.dialog-actions {
  display: flex;
  gap: 12px;
  padding: 20px 24px;
  background: #f9fafb;
  border-top: 1px solid #e5e7eb;
  justify-content: flex-end;
}

.cancel-btn,
.confirm-btn {
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
  display: flex;
  align-items: center;
  gap: 6px;
}

.cancel-btn {
  background: #f3f4f6;
  color: #374151;
}

.cancel-btn:hover {
  background: #e5e7eb;
}

.confirm-btn {
  background: #3b82f6;
  color: white;
}

.confirm-btn:hover:not(:disabled) {
  background: #2563eb;
}

.confirm-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive design */
@media (max-width: 640px) {
  .task-name-dialog {
    width: 95%;
    margin: 20px;
  }

  .dialog-header,
  .dialog-content,
  .dialog-actions {
    padding-left: 16px;
    padding-right: 16px;
  }

  .dialog-actions {
    flex-direction: column;
  }

  .cancel-btn,
  .confirm-btn {
    width: 100%;
    justify-content: center;
  }
}
</style>
