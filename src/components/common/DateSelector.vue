<template>
  <div
    class="date-selector"
    ref="dateSelectorRef"
    :class="{ 'is-disabled': disabled, 'is-loading': loading }"
  >
    <div
      class="date-input-wrapper"
      @click="!disabled && !loading && toggleCalendar()"
      :tabindex="disabled ? -1 : 0"
      @keydown.enter="!disabled && !loading && toggleCalendar()"
      @keydown.space.prevent="!disabled && !loading && toggleCalendar()"
      role="combobox"
      :aria-expanded="isOpen"
      :aria-haspopup="true"
      :aria-controls="'calendar-dropdown'"
    >
      <input
        class="date-input"
        :value="displayValue"
        readonly
        :placeholder="placeholder"
        :disabled="disabled || loading"
        :aria-label="ariaLabel"
      />
      <span class="calendar-icon" :class="{ 'is-loading': loading }">
        <span v-if="loading" class="loading-spinner"></span>
        <span v-else></span>
      </span>
    </div>

    <div
      v-if="isOpen"
      class="calendar-dropdown"
      id="calendar-dropdown"
      role="dialog"
      aria-modal="true"
      aria-label="日期选择器"
    >
      <!-- Quick Presets for Range Mode -->
      <div v-if="internalMode === 'range'" class="quick-presets">
        <button
          v-for="preset in quickPresets"
          :key="preset.label"
          class="preset-btn"
          @click="applyPreset(preset)"
          :disabled="loading"
        >
          {{ preset.label }}
        </button>
      </div>

      <div class="calendar-header">
        <button class="calendar-nav-btn" @click="prevMonth" :disabled="loading" aria-label="上个月">
          «
        </button>
        <span class="current-month">{{ currentMonthYear }}</span>
        <button class="calendar-nav-btn" @click="nextMonth" :disabled="loading" aria-label="下个月">
          »
        </button>
      </div>

      <div class="calendar-mode-toggle" v-if="internalMode === 'range'">
        <button
          :class="['mode-btn', { active: activePanel === 'start' }]"
          @click="activePanel = 'start'"
          :disabled="loading"
          :aria-pressed="activePanel === 'start'"
        >
          开始日期
        </button>
        <button
          :class="['mode-btn', { active: activePanel === 'end' }]"
          @click="activePanel = 'end'"
          :disabled="loading"
          :aria-pressed="activePanel === 'end'"
        >
          结束日期
        </button>
      </div>

      <div class="calendar-grid">
        <div class="weekdays" role="rowgroup">
          <span v-for="day in weekDays" :key="day" role="columnheader" :aria-label="'星期' + day">{{
            day
          }}</span>
        </div>
        <div class="days" role="grid">
          <button
            v-for="day in calendarDays"
            :key="day.date"
            :class="[
              'day-btn',
              {
                'other-month': !day.currentMonth,
                selected: isSelected(day.date),
                'in-range': isInRange(day.date),
                today: isToday(day.date),
                disabled: isDisabled(day.date),
                hover: isHovered(day.date),
              },
            ]"
            @click="selectDate(day.date)"
            @mouseenter="handleDayHover(day.date)"
            @mouseleave="handleDayHover(null)"
            :disabled="isDisabled(day.date) || loading"
            :aria-selected="isSelected(day.date)"
            :aria-label="formatDate(day.date)"
            role="gridcell"
          >
            {{ day.day }}
          </button>
        </div>
      </div>

      <!-- Calendar Footer for Range Mode -->
      <div class="calendar-footer" v-if="internalMode === 'range'">
        <div class="selected-range" v-if="tempRange.start || tempRange.end">
          <span class="range-label">已选择：</span>
          <span class="range-value">
            {{ formatDate(tempRange.start) }} - {{ formatDate(tempRange.end) }}
          </span>
        </div>
        <div class="footer-buttons">
          <button class="clear-btn" @click="clearSelection" :disabled="loading">清除</button>
          <button
            class="confirm-btn"
            @click="confirmSelection"
            :disabled="!isRangeValid || loading"
          >
            确定
          </button>
        </div>
      </div>

      <!-- Calendar Footer for Multiple Mode -->
      <div class="calendar-footer" v-if="internalMode === 'multiple'">
        <div
          class="selected-multiple"
          v-if="Array.isArray(props.modelValue) && props.modelValue.length"
        >
          <span class="range-label">已选择：</span>
          <span class="range-value">
            {{
              props.modelValue
                .map((d) => formatDate(d))
                .sort()
                .join(', ')
            }}
          </span>
        </div>
        <div class="footer-buttons">
          <button class="clear-btn" @click="clearSelection" :disabled="loading">清除</button>
          <button class="confirm-btn" @click="isOpen = false" :disabled="loading">确定</button>
        </div>
      </div>

      <!-- Validation Message -->
      <div v-if="validationMessage" class="validation-message" :class="validationType">
        {{ validationMessage }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'

const props = defineProps({
  modelValue: {
    type: [Array],
    default: null,
  },
  mode: {
    type: String,
    default: 'range',
    validator: (value) => ['range', 'multiple'].includes(value),
  },
  placeholder: {
    type: String,
    default: '选择日期',
  },
  minDate: {
    type: Date,
    default: null,
  },
  maxDate: {
    type: Date,
    default: null,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  loading: {
    type: Boolean,
    default: false,
  },
  dateFormat: {
    type: String,
    default: 'YYYY-MM-DD',
  },
  ariaLabel: {
    type: String,
    default: '日期选择器',
  },
  validateRange: {
    type: Function,
    default: null,
  },
})

const emit = defineEmits(['update:modelValue', 'validation-change'])

const dateSelectorRef = ref(null)
const isOpen = ref(false)
const currentDate = ref(new Date())
const activePanel = ref('start')
const tempRange = ref({ start: null, end: null })
const hoveredDate = ref(null)
const validationMessage = ref('')
const validationType = ref('error')
const internalMode = ref(props.mode)

const weekDays = ['日', '一', '二', '三', '四', '五', '六']

const quickPresets = [
  {
    label: '最近7天',
    getRange: () => {
      const end = new Date()
      const start = new Date()
      start.setDate(start.getDate() - 6)
      return { start, end }
    },
  },
  {
    label: '最近30天',
    getRange: () => {
      const end = new Date()
      const start = new Date()
      start.setDate(start.getDate() - 29)
      return { start, end }
    },
  },
  {
    label: '本月',
    getRange: () => {
      const now = new Date()
      const start = new Date(now.getFullYear(), now.getMonth(), 1)
      const end = new Date(now.getFullYear(), now.getMonth() + 1, 0)
      return { start, end }
    },
  },
  {
    label: '上月',
    getRange: () => {
      const now = new Date()
      const start = new Date(now.getFullYear(), now.getMonth() - 1, 1)
      const end = new Date(now.getFullYear(), now.getMonth(), 0)
      return { start, end }
    },
  },
]

const currentMonthYear = computed(() => {
  return `${currentDate.value.getFullYear()}年${currentDate.value.getMonth() + 1}月`
})

const displayValue = computed(() => {
  if (!props.modelValue) return ''
  if (internalMode.value === 'range') {
    if (!Array.isArray(props.modelValue) || props.modelValue.length !== 2) return ''
    return `${formatDate(props.modelValue[0])} - ${formatDate(props.modelValue[1])}`
  } else if (internalMode.value === 'multiple') {
    if (!Array.isArray(props.modelValue) || props.modelValue.length === 0) return ''
    const unique = Array.from(
      new Set(
        props.modelValue.map((d) => {
          // Handle both Date objects and yyyymmdd strings
          if (d instanceof Date) {
            return formatDate(d)
          } else if (typeof d === 'string' && d.length === 8) {
            const parsedDate = parseYYYYMMDD(d)
            return parsedDate ? formatDate(parsedDate) : d
          }
          return d
        }),
      ),
    ).sort()
    return unique.join(', ')
  }
  return ''
})

const calendarDays = computed(() => {
  let year = currentDate.value.getFullYear()
  let month = currentDate.value.getMonth()
  if (isNaN(year) || isNaN(month)) {
    year = new Date().getFullYear()
    month = new Date().getMonth()
  }
  const firstDay = new Date(year, month, 1)
  const lastDay = new Date(year, month + 1, 0)
  const firstDayOfWeek = firstDay.getDay()
  const prevMonthLastDay = new Date(year, month, 0).getDate()
  const prevMonthDays = Array.from({ length: firstDayOfWeek }, (_, i) => {
    const date = new Date(year, month - 1, prevMonthLastDay - firstDayOfWeek + i + 1)
    return {
      date,
      day: date.getDate(),
      currentMonth: false,
    }
  })
  const currentMonthDays = Array.from({ length: lastDay.getDate() }, (_, i) => {
    const date = new Date(year, month, i + 1)
    return {
      date,
      day: date.getDate(),
      currentMonth: true,
    }
  })
  const remainingDays = 42 - (prevMonthDays.length + currentMonthDays.length)
  const nextMonthDays = Array.from({ length: remainingDays }, (_, i) => {
    const date = new Date(year, month + 1, i + 1)
    return {
      date,
      day: date.getDate(),
      currentMonth: false,
    }
  })
  return [...prevMonthDays, ...currentMonthDays, ...nextMonthDays]
})

const formatDate = (date) => {
  if (!date) return ''
  let d = date
  if (!(d instanceof Date)) {
    d = new Date(d)
  }
  if (isNaN(d.getTime())) return ''
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  return props.dateFormat.replace('YYYY', year).replace('MM', month).replace('DD', day)
}

// Format date for internal storage (yyyymmdd format)
const formatDateForStorage = (date) => {
  if (!date) return ''
  let d = date
  if (!(d instanceof Date)) {
    d = new Date(d)
  }
  if (isNaN(d.getTime())) return ''
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  return `${year}${month}${day}`
}

// Parse yyyymmdd format string to Date object
const parseYYYYMMDD = (dateStr) => {
  if (!dateStr || typeof dateStr !== 'string' || dateStr.length !== 8) {
    return null
  }
  const year = parseInt(dateStr.substring(0, 4), 10)
  const month = parseInt(dateStr.substring(4, 6), 10) - 1 // Month is 0-indexed
  const day = parseInt(dateStr.substring(6, 8), 10)
  const date = new Date(year, month, day)
  // Validate the date
  if (date.getFullYear() !== year || date.getMonth() !== month || date.getDate() !== day) {
    return null
  }
  return date
}

const toggleCalendar = () => {
  if (props.disabled || props.loading) return
  isOpen.value = !isOpen.value
  if (isOpen.value) {
    if (internalMode.value === 'range') {
      if (Array.isArray(props.modelValue) && props.modelValue.length === 2) {
        tempRange.value = {
          start: props.modelValue[0],
          end: props.modelValue[1],
        }
      } else {
        tempRange.value = { start: null, end: null }
      }
    } else if (internalMode.value === 'multiple') {
      if (Array.isArray(props.modelValue) && props.modelValue.length > 0) {
        currentDate.value = new Date(props.modelValue[0])
      } else {
        currentDate.value = new Date()
      }
    }
  }
}

const prevMonth = () => {
  if (props.loading) return
  currentDate.value = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth() - 1)
}

const nextMonth = () => {
  if (props.loading) return
  currentDate.value = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth() + 1)
}

const isSelected = (date) => {
  if (!date) return false
  if (internalMode.value === 'range') {
    const isInModelValue =
      Array.isArray(props.modelValue) &&
      props.modelValue.length === 2 &&
      props.modelValue[0] instanceof Date &&
      props.modelValue[1] instanceof Date &&
      (date.toDateString() === props.modelValue[0].toDateString() ||
        date.toDateString() === props.modelValue[1].toDateString())
    const isInTempRange =
      tempRange.value?.start instanceof Date &&
      (date.toDateString() === tempRange.value.start.toDateString() ||
        (tempRange.value.end instanceof Date &&
          date.toDateString() === tempRange.value.end.toDateString()))
    return isInModelValue || isInTempRange
  } else if (internalMode.value === 'multiple') {
    return (
      Array.isArray(props.modelValue) &&
      props.modelValue.some((d) => {
        // Compare using yyyymmdd format for consistency
        const dd = d instanceof Date ? formatDateForStorage(d) : d
        return dd === formatDateForStorage(date)
      })
    )
  }
  return false
}

const isInRange = (date) => {
  if (!date || internalMode.value !== 'range') return false
  if (tempRange.value?.start instanceof Date) {
    const start = tempRange.value.start
    const end = tempRange.value.end instanceof Date ? tempRange.value.end : hoveredDate.value
    if (end instanceof Date && date > start && date < end) return true
  }
  if (
    Array.isArray(props.modelValue) &&
    props.modelValue.length === 2 &&
    props.modelValue[0] instanceof Date &&
    props.modelValue[1] instanceof Date
  ) {
    const start = props.modelValue[0]
    const end = props.modelValue[1]
    return date > start && date < end
  }
  return false
}

const isHovered = (date) => {
  if (
    !date ||
    internalMode.value !== 'range' ||
    (!tempRange.value?.start) instanceof Date ||
    tempRange.value?.end instanceof Date
  ) {
    return false
  }
  return (
    hoveredDate.value instanceof Date && date.toDateString() === hoveredDate.value.toDateString()
  )
}

const isToday = (date) => {
  if (!date) return false
  const today = new Date()
  return date.toDateString() === today.toDateString()
}

const isDisabled = (date) => {
  if (!date) return true
  if (props.minDate instanceof Date && date < props.minDate) return true
  if (props.maxDate instanceof Date && date > props.maxDate) return true
  return false
}

const handleDayHover = (date) => {
  if (internalMode.value === 'range' && tempRange.value.start && !tempRange.value.end) {
    hoveredDate.value = date
  }
}

const selectDate = (date) => {
  if ((!date) instanceof Date || props.disabled || props.loading || isDisabled(date)) return
  if (internalMode.value === 'range') {
    if (activePanel.value === 'start') {
      tempRange.value = { start: new Date(date), end: null }
      activePanel.value = 'end'
    } else {
      if (date < tempRange.value.start) {
        tempRange.value = {
          start: new Date(date),
          end: new Date(tempRange.value.start),
        }
      } else {
        tempRange.value = {
          start: new Date(tempRange.value.start),
          end: new Date(date),
        }
      }
      emit('update:modelValue', [tempRange.value.start, tempRange.value.end])
      isOpen.value = false
    }
  } else if (internalMode.value === 'multiple') {
    let arr = Array.isArray(props.modelValue) ? [...props.modelValue] : []
    // Ensure all dates are in yyyymmdd format for storage
    arr = arr.map((d) => (d instanceof Date ? formatDateForStorage(d) : d))
    const dateStr = formatDateForStorage(date)
    const idx = arr.findIndex((d) => d === dateStr)
    if (idx > -1) {
      arr.splice(idx, 1)
    } else {
      arr.push(dateStr)
    }
    arr = Array.from(new Set(arr))
    emit('update:modelValue', arr)
  }
}

const applyPreset = (preset) => {
  if (props.disabled || props.loading) return
  const { start, end } = preset.getRange()
  if (!(start instanceof Date) || !(end instanceof Date)) return

  tempRange.value = {
    start: new Date(start),
    end: new Date(end),
  }

  if (props.validateRange) {
    const validation = props.validateRange(tempRange.value.start, tempRange.value.end)
    if (validation.valid) {
      emit('update:modelValue', [tempRange.value.start, tempRange.value.end])
      isOpen.value = false
    }
  } else {
    emit('update:modelValue', [tempRange.value.start, tempRange.value.end])
    isOpen.value = false
  }
}

const clearSelection = () => {
  if (props.disabled || props.loading) return
  emit('update:modelValue', [])
  if (internalMode.value === 'range') {
    tempRange.value = { start: null, end: null }
  }
  currentDate.value = new Date()
  validationMessage.value = ''
  activePanel.value = 'start'
  isOpen.value = false
}

const confirmSelection = () => {
  if (props.disabled || props.loading || !isRangeValid.value) return
  if (tempRange.value.start && tempRange.value.end) {
    emit('update:modelValue', [tempRange.value.start, tempRange.value.end])
    isOpen.value = false
  }
}

const isRangeValid = computed(() => {
  if (internalMode.value !== 'range') return true
  if (!tempRange.value.start || !tempRange.value.end) return false
  if (props.validateRange) {
    const validation = props.validateRange(tempRange.value.start, tempRange.value.end)
    return validation.valid
  }
  return true
})

watch(
  () => props.modelValue,
  (newValue) => {
    if (
      internalMode.value === 'range' &&
      Array.isArray(newValue) &&
      newValue.length === 2 &&
      newValue[0] instanceof Date &&
      newValue[1] instanceof Date
    ) {
      tempRange.value = {
        start: new Date(newValue[0]),
        end: new Date(newValue[1]),
      }
    }
  },
  { immediate: true },
)

watch(internalMode, (val) => {
  if (val === 'range') {
    if (!Array.isArray(props.modelValue) || props.modelValue.length !== 2) {
      tempRange.value = { start: null, end: null }
      currentDate.value = new Date()
    } else {
      tempRange.value = {
        start: new Date(props.modelValue[0]),
        end: new Date(props.modelValue[1]),
      }
      currentDate.value = new Date(props.modelValue[0])
    }
  } else if (val === 'multiple') {
    if (!Array.isArray(props.modelValue)) {
      emit('update:modelValue', [])
    }
    currentDate.value = new Date()
    tempRange.value = { start: null, end: null }
  }
})

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
  document.removeEventListener('keydown', handleKeydown)
})

function handleClickOutside(event) {
  if (dateSelectorRef.value && !dateSelectorRef.value.contains(event.target)) {
    isOpen.value = false
  }
}

function handleKeydown(event) {
  if (!isOpen.value) return
  // Keyboard navigation logic can be added here if needed
}
</script>

<style scoped>
.date-selector {
  position: relative;
  width: 100%;
}

.date-selector.is-disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.date-selector.is-loading .date-input {
  cursor: wait;
}

.date-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.date-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  background: #fff;
  cursor: pointer;
  transition: all 0.2s;
}

.date-input:focus {
  outline: none;
  border-color: #1976d2;
  box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.1);
}

.date-input:disabled {
  background: #f5f5f5;
  cursor: not-allowed;
}

.calendar-icon {
  position: absolute;
  right: 12px;
  color: #666;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
}

.calendar-icon.is-loading {
  cursor: wait;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #1976d2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.calendar-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  margin-top: 4px;
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  min-width: 280px;
  padding: 12px;
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.current-month {
  font-weight: 500;
  color: #333;
}

.calendar-nav-btn {
  background: none;
  border: none;
  color: #666;
  font-size: 16px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s;
}

.calendar-nav-btn:hover:not(:disabled) {
  background: #f5f5f5;
  color: #1976d2;
}

.calendar-nav-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.calendar-mode-toggle {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
}

.mode-btn {
  flex: 1;
  padding: 6px;
  border: 1px solid #ddd;
  background: #fff;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  color: #666;
  transition: all 0.2s;
}

.mode-btn:hover:not(:disabled) {
  border-color: #1976d2;
  color: #1976d2;
}

.mode-btn.active {
  background: #1976d2;
  color: #fff;
  border-color: #1976d2;
}

.mode-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.calendar-grid {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  text-align: center;
  font-size: 12px;
  color: #666;
}

.days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 4px;
}

.day-btn {
  aspect-ratio: 1;
  border: none;
  background: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  position: relative;
}

.day-btn:hover:not(:disabled) {
  background: #f5f5f5;
}

.day-btn.other-month {
  color: #ccc;
}

.day-btn.selected {
  background: #1976d2;
  color: #fff;
  font-weight: 500;
}

.day-btn.in-range {
  background: #e3f2fd;
  color: #1976d2;
}

.day-btn.today {
  border: 1px solid #1976d2;
  color: #1976d2;
  font-weight: 500;
}

.day-btn.disabled {
  color: #ccc;
  cursor: not-allowed;
}

.day-btn.hover {
  background: #e3f2fd;
  color: #1976d2;
}

.calendar-footer {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #eee;
}

.selected-range {
  margin-bottom: 8px;
  font-size: 13px;
  color: #666;
}

.range-label {
  color: #999;
}

.range-value {
  color: #1976d2;
  font-weight: 500;
}

.footer-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.clear-btn,
.confirm-btn {
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s;
}

.clear-btn {
  background: none;
  border: 1px solid #ddd;
  color: #666;
}

.confirm-btn {
  background: #1976d2;
  border: none;
  color: #fff;
}

.confirm-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.clear-btn:hover:not(:disabled) {
  background: #f5f5f5;
  border-color: #1976d2;
  color: #1976d2;
}

.confirm-btn:hover:not(:disabled) {
  background: #1565c0;
}

.validation-message {
  margin-top: 8px;
  padding: 8px;
  border-radius: 4px;
  font-size: 12px;
  text-align: center;
}

.validation-message.error {
  background: #ffebee;
  color: #d32f2f;
}

.validation-message.warning {
  background: #fff3e0;
  color: #f57c00;
}

.validation-message.success {
  background: #e8f5e9;
  color: #2e7d32;
}

/* Mobile Responsiveness */
@media (max-width: 480px) {
  .calendar-dropdown {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90%;
    max-width: 320px;
    margin: 0;
  }

  .calendar-mode-toggle {
    flex-direction: column;
  }

  .mode-btn {
    width: 100%;
  }

  .footer-buttons {
    flex-direction: column;
  }

  .clear-btn,
  .confirm-btn {
    width: 100%;
  }
}

.quick-presets {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 1px solid #eee;
}
.preset-btn {
  padding: 4px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: #fff;
  color: #666;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}
.preset-btn:hover:not(:disabled) {
  background: #f5f5f5;
  border-color: #1976d2;
  color: #1976d2;
}
.preset-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
</style>
