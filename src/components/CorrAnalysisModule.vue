<template>
  <div class="corr-module">
    <div class="corr-title">
      <span style="color: #1976d2; font-weight: bold">🔗 相关性分析</span>
      <button
        class="corr-arrow"
        :disabled="selectedObjects.length !== 2"
        @click="handleAnalysis"
        :class="{ analyzing: isAnalyzing, error: error }"
      >
        »
      </button>
    </div>
    <div class="corr-selector">
      <CustomSelect v-model="selectedObjects" :groups="selectGroups" />
    </div>
    <div class="corr-hint" v-if="selectedObjects.length !== 2">请选择2个对象进行相关性分析</div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import CustomSelect from './common/CustomSelect.vue'
import { useCorrAnalysis } from '../composables/useCorrAnalysis'
import { useStates } from '../store/states'
import { v4 as uuidv4 } from 'uuid'

const { availableObjects, selectedObjects, runAnalysis } = useCorrAnalysis()
const statesStore = useStates()
const { updateComponent } = statesStore

const isAnalyzing = ref(false)
const error = ref(false)

// Format data for CustomSelect component
const selectGroups = computed(() => [
  {
    label: '分析对象',
    options: availableObjects.value.map((obj) => ({
      value: obj.value,
      label: `${obj.label} (${obj.type === 'indicator' ? '指标' : '维度'})`,
    })),
  },
])

// Watch for changes in available options to filter out invalid selections
watch(
  availableObjects,
  (newObjects) => {
    const validObjects = selectedObjects.value.filter((obj) =>
      newObjects.some((availableObj) => availableObj.value === obj),
    )

    if (validObjects.length !== selectedObjects.value.length) {
      selectedObjects.value = validObjects
    }
  },
  { deep: true },
)

const handleAnalysis = async () => {
  if (selectedObjects.value.length !== 2 || isAnalyzing.value) return

  try {
    isAnalyzing.value = true
    error.value = false
    const result = await runAnalysis()

    // Create a new result module
    const componentId = uuidv4()
    const newComponent = {
      componentId,
      componentName: 'corr-result',
      result: result,
    }
    // Update store with the new result module
    updateComponent(componentId, newComponent)
  } catch (err) {
    console.error('Correlation analysis failed:', err)
    error.value = true
  } finally {
    isAnalyzing.value = false
  }
}
</script>

<style scoped>
.corr-module {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
  width: 100%;
}

.corr-title {
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  justify-content: center;
}

.corr-arrow {
  color: #8bc34a;
  font-size: 1.5rem;
  font-weight: bold;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.corr-arrow:hover:not(:disabled) {
  background: #e8f5e9;
  transform: scale(1.1);
}

.corr-arrow:disabled {
  color: #ccc;
  cursor: not-allowed;
}

.corr-arrow.analyzing {
  animation: spin 1s linear infinite;
  color: #1976d2;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.corr-arrow.error {
  color: #f44336;
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%,
  100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5px);
  }
  75% {
    transform: translateX(5px);
  }
}

.corr-selector {
  width: 100%;
  display: flex;
  justify-content: center;
}

.corr-hint {
  font-size: 0.9rem;
  color: #666;
  text-align: center;
  font-style: italic;
}
</style>
