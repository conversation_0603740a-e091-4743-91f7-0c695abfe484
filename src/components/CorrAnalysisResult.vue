<template>
  <div class="corr-result">
    <div class="result-header">
      <span class="result-title">🔗 相关性分析结果</span>
      <span class="correlation-badge" :class="correlationClass">
        {{ correlationStrength }}
      </span>
    </div>

    <div class="result-grid">
      <!-- 相关系数 -->
      <div class="result-card">
        <div class="card-header">
          <span class="card-icon">📊</span>
          <span class="card-title">相关系数</span>
        </div>
        <div class="card-content">
          <div class="metric">
            <span class="metric-label">皮尔逊系数</span>
            <span class="metric-value">{{ formatCorrelation(correlationCoefficient) }}</span>
          </div>
          <div class="correlation-bar">
            <div
              class="correlation-fill"
              :style="{ width: `${Math.abs(correlationCoefficient) * 100}%` }"
              :class="correlationFillClass"
            ></div>
          </div>
          <span class="correlation-label">{{ correlationDirection }}</span>
        </div>
      </div>

      <!-- 显著性 -->
      <div class="result-card">
        <div class="card-header">
          <span class="card-icon">🎯</span>
          <span class="card-title">显著性</span>
        </div>
        <div class="card-content">
          <div class="metric">
            <span class="metric-label">P值</span>
            <span class="metric-value">{{ formatPValue(pValue) }}</span>
          </div>
          <div class="significance-indicator">
            <span class="significance-badge" :class="significanceClass">
              {{ significanceText }}
            </span>
          </div>
        </div>
      </div>

      <!-- 样本数量 -->
      <div class="result-card">
        <div class="card-header">
          <span class="card-icon">📈</span>
          <span class="card-title">样本信息</span>
        </div>
        <div class="card-content">
          <div class="metric">
            <span class="metric-label">样本数量</span>
            <span class="metric-value">{{ sampleSize }}</span>
          </div>
          <div class="metric">
            <span class="metric-label">自由度</span>
            <span class="metric-value">{{ degreesOfFreedom }}</span>
          </div>
        </div>
      </div>

      <!-- 分析对象 -->
      <div class="result-card">
        <div class="card-header">
          <span class="card-icon">🎯</span>
          <span class="card-title">分析对象</span>
        </div>
        <div class="card-content">
          <div class="object-item">
            <span class="object-label">对象1:</span>
            <span class="object-value">{{ object1 }}</span>
          </div>
          <div class="object-item">
            <span class="object-label">对象2:</span>
            <span class="object-value">{{ object2 }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { storeToRefs } from 'pinia'
import { useStates } from '../store/states'

const props = defineProps({
  result: {
    type: Object,
    required: true,
    validator: (value) => {
      return value && typeof value === 'object' && 'result' in value
    },
  },
})

const statesStore = useStates()
const { states } = storeToRefs(statesStore)

// Get the correlation analysis component from store
const corrComponent = computed(() =>
  states.value.find((state) => state.componentName === 'corrAnalysis'),
)

// Extract values from result (assuming backend returns these fields)
const correlationCoefficient = computed(() => props.result.result.correlation_coefficient || 0)
const pValue = computed(() => props.result.result.p_value || 1)
const sampleSize = computed(() => props.result.result.sample_size || 0)
const degreesOfFreedom = computed(() => props.result.result.degrees_of_freedom || 0)

// Get object1 and object2 from the corrAnalysis component's selectedObjects
const object1 = computed(() => {
  const selectedObjects = corrComponent.value?.selectedObjects || []
  return selectedObjects[0] || '未知'
})

const object2 = computed(() => {
  const selectedObjects = corrComponent.value?.selectedObjects || []
  return selectedObjects[1] || '未知'
})

// Computed properties for styling and display
const correlationStrength = computed(() => {
  const absCorr = Math.abs(correlationCoefficient.value)
  if (absCorr >= 0.8) return '强相关'
  if (absCorr >= 0.5) return '中等相关'
  if (absCorr >= 0.3) return '弱相关'
  return '无相关'
})

const correlationClass = computed(() => {
  const absCorr = Math.abs(correlationCoefficient.value)
  if (absCorr >= 0.8) return 'strong'
  if (absCorr >= 0.5) return 'medium'
  if (absCorr >= 0.3) return 'weak'
  return 'none'
})

const correlationFillClass = computed(() => {
  const absCorr = Math.abs(correlationCoefficient.value)
  if (absCorr >= 0.8) return 'strong-fill'
  if (absCorr >= 0.5) return 'medium-fill'
  if (absCorr >= 0.3) return 'weak-fill'
  return 'none-fill'
})

const correlationDirection = computed(() => {
  if (correlationCoefficient.value > 0) return '正相关'
  if (correlationCoefficient.value < 0) return '负相关'
  return '无相关'
})

const significanceText = computed(() => {
  if (pValue.value < 0.001) return '极显著'
  if (pValue.value < 0.01) return '高度显著'
  if (pValue.value < 0.05) return '显著'
  return '不显著'
})

const significanceClass = computed(() => {
  if (pValue.value < 0.001) return 'highly-significant'
  if (pValue.value < 0.01) return 'very-significant'
  if (pValue.value < 0.05) return 'significant'
  return 'not-significant'
})

// Format helpers
const formatCorrelation = (value) => value.toFixed(3)
const formatPValue = (value) => {
  if (value < 0.001) return '< 0.001'
  return value.toFixed(3)
}
</script>

<style scoped>
.corr-result {
  background: #fff;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  width: 100%;
  min-width: 400px;
  max-width: 600px;
}

.result-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
}

.result-title {
  font-size: 1.1rem;
  font-weight: bold;
  color: #1976d2;
}

.correlation-badge {
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
}

.correlation-badge.strong {
  background: #e8f5e9;
  color: #2e7d32;
}

.correlation-badge.medium {
  background: #fff3e0;
  color: #f57c00;
}

.correlation-badge.weak {
  background: #e3f2fd;
  color: #1976d2;
}

.correlation-badge.none {
  background: #f5f5f5;
  color: #666;
}

.result-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.result-card {
  background: #f8f9fa;
  border-radius: 10px;
  padding: 1rem;
  transition:
    transform 0.2s,
    box-shadow 0.2s;
}

.result-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.card-icon {
  font-size: 1.2rem;
}

.card-title {
  font-weight: 500;
  color: #424242;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.metric {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.metric-label {
  color: #666;
  font-size: 0.9rem;
}

.metric-value {
  font-weight: 500;
  color: #1976d2;
}

.correlation-bar {
  height: 6px;
  background: #e0e0e0;
  border-radius: 3px;
  overflow: hidden;
}

.correlation-fill {
  height: 100%;
  border-radius: 3px;
  transition: width 0.3s ease;
}

.correlation-fill.strong-fill {
  background: #4caf50;
}

.correlation-fill.medium-fill {
  background: #ff9800;
}

.correlation-fill.weak-fill {
  background: #2196f3;
}

.correlation-fill.none-fill {
  background: #9e9e9e;
}

.correlation-label {
  font-size: 0.85rem;
  color: #666;
  text-align: right;
}

.significance-indicator {
  display: flex;
  justify-content: flex-end;
}

.significance-badge {
  padding: 0.2rem 0.6rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.significance-badge.highly-significant {
  background: #e8f5e9;
  color: #2e7d32;
}

.significance-badge.very-significant {
  background: #e8f5e9;
  color: #388e3c;
}

.significance-badge.significant {
  background: #fff3e0;
  color: #f57c00;
}

.significance-badge.not-significant {
  background: #f5f5f5;
  color: #666;
}

.object-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.3rem;
}

.object-label {
  color: #666;
  font-size: 0.9rem;
}

.object-value {
  font-weight: 500;
  color: #1976d2;
  font-size: 0.9rem;
}

@media (max-width: 768px) {
  .result-grid {
    grid-template-columns: 1fr;
  }
  .corr-result {
    min-width: 100%;
    max-width: 100%;
  }
}
</style>
