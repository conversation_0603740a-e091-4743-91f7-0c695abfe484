<template>
  <div class="index-selector">
    <div class="selector-body">
      <div class="category-levels">
        <div class="level-label">一级分类</div>
        <div class="level level-1">
          <div
            v-for="cat1 in categories"
            :key="cat1.id"
            :class="['cat-item', { active: cat1.id === selectedCat1 }]"
            @click="selectCat1(cat1.id)"
          >
            {{ cat1.name }}
          </div>
        </div>
        <div v-if="cat2List.length">
          <div class="level-label">二级分类</div>
          <div class="level level-2">
            <div
              v-for="cat2 in cat2List"
              :key="cat2.id"
              :class="['cat-item', { active: cat2.id === selectedCat2 }]"
              @click="selectCat2(cat2.id)"
            >
              {{ cat2.name }}
            </div>
          </div>
        </div>
        <div v-if="cat3List.length">
          <div class="level-label">三级分类</div>
          <div class="level level-3">
            <div
              v-for="cat3 in cat3List"
              :key="cat3.id"
              :class="['cat-item', { active: cat3.id === selectedCat3 }]"
              @click="selectCat3(cat3.id)"
            >
              {{ cat3.name }}
            </div>
          </div>
        </div>
      </div>
      <div class="index-list" v-if="indexList.length">
        <div
          v-for="idx in indexList"
          :key="idx.id"
          class="index-item"
          @click="$emit('select', idx)"
        >
          <span>{{ idx.name }}</span>
        </div>
      </div>
      <div v-else class="empty-tip">
        <div v-if="availableIndicators.length === 0">暂无可用指标</div>
        <div v-else>请选择分类以查看指标</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { storeToRefs } from 'pinia'
import { useStates } from '../store/states'

const statesStore = useStates()
const { availableIndicators } = storeToRefs(statesStore)

// Organize indicators by categories
const categories = computed(() => {
  const categoryMap = new Map()

  availableIndicators.value.forEach((indicator) => {
    if (!indicator.is_permission) return // Skip non-permitted indicators

    const cat1Key = indicator.category_lv_1
    const cat2Key = indicator.category_lv_2 || 'default'
    const cat3Key = indicator.category_lv_3 || 'default'

    if (!categoryMap.has(cat1Key)) {
      categoryMap.set(cat1Key, {
        id: cat1Key,
        name: cat1Key,
        children: new Map(),
      })
    }

    const cat1 = categoryMap.get(cat1Key)
    if (!cat1.children.has(cat2Key)) {
      cat1.children.set(cat2Key, {
        id: `${cat1Key}-${cat2Key}`,
        name: cat2Key,
        children: new Map(),
      })
    }

    const cat2 = cat1.children.get(cat2Key)
    if (!cat2.children.has(cat3Key)) {
      cat2.children.set(cat3Key, {
        id: `${cat1Key}-${cat2Key}-${cat3Key}`,
        name: cat3Key,
        indices: [],
      })
    }

    const cat3 = cat2.children.get(cat3Key)
    cat3.indices.push({
      id: indicator.id,
      name: indicator.display_name,
      originalData: indicator,
    })
  })

  // Convert Maps to Arrays
  const result = Array.from(categoryMap.values()).map((cat1) => ({
    ...cat1,
    children: Array.from(cat1.children.values()).map((cat2) => ({
      ...cat2,
      children: Array.from(cat2.children.values()),
    })),
  }))

  console.log('IndexSelector categories computed:', result)
  return result
})

const selectedCat1 = ref(null)
const selectedCat2 = ref(null)
const selectedCat3 = ref(null)

const cat2List = computed(() => {
  const cat1 = categories.value.find((c) => c.id === selectedCat1.value)
  return cat1 ? cat1.children : []
})
const cat3List = computed(() => {
  const cat2 = cat2List.value.find((c) => c.id === selectedCat2.value)
  return cat2 ? cat2.children : []
})
const indexList = computed(() => {
  const cat3 = cat3List.value.find((c) => c.id === selectedCat3.value)
  return cat3 ? cat3.indices : []
})

function selectCat1(id) {
  selectedCat1.value = id
  selectedCat2.value = null
  selectedCat3.value = null
}
function selectCat2(id) {
  selectedCat2.value = id
  selectedCat3.value = null
}
function selectCat3(id) {
  selectedCat3.value = id
}
</script>

<style scoped>
.index-selector {
  width: 100%;
  height: 100%;
  background: transparent;
  display: flex;
  flex-direction: column;
}
.selector-body {
  display: flex;
  flex-direction: row;
  flex: 1;
  padding: 20px;
  gap: 24px;
  overflow: hidden;
}
.category-levels {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 200px;
  max-width: 220px;
}
.level-label {
  font-size: 0.98rem;
  font-weight: 500;
  color: #1976d2;
  margin: 8px 0 4px 0;
  padding-left: 2px;
}
.level {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 4px;
}
.cat-item {
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  background: #f7faff;
  color: #333;
  transition:
    background 0.2s,
    color 0.2s;
}
.cat-item.active {
  background: #1976d2;
  color: #fff;
}
.index-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
  min-width: 200px;
}
.index-item {
  padding: 10px 16px;
  border-radius: 6px;
  background: #f5f5f5;
  cursor: pointer;
  font-size: 1rem;
  color: #1976d2;
  transition:
    background 0.2s,
    color 0.2s;
}
.index-item:hover {
  background: #1976d2;
  color: #fff;
}
.empty-tip {
  color: #aaa;
  font-size: 1rem;
  margin-top: 40px;
}
</style>
