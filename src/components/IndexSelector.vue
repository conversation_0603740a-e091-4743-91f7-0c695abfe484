<template>
  <div class="index-selector">
    <div class="selector-body">
      <div class="category-levels">
        <div class="level-label">一级分类</div>
        <div class="level level-1">
          <div v-if="isLoadingCategories" class="loading-item">加载分类中...</div>
          <div
            v-for="cat1 in categories"
            :key="cat1.id"
            :class="['cat-item', { active: cat1.name === selectedCat1 }]"
            @click="selectCat1(cat1.name)"
          >
            {{ cat1.name }}
          </div>
          <div v-if="!isLoadingCategories && categories.length === 0" class="empty-item">
            暂无可用分类
          </div>
        </div>
        <div v-if="cat2List.length">
          <div class="level-label">二级分类</div>
          <div class="level level-2">
            <div
              v-for="cat2 in cat2List"
              :key="cat2.id"
              :class="['cat-item', { active: cat2.name === selectedCat2 }]"
              @click="selectCat2(cat2.name)"
            >
              {{ cat2.name }}
            </div>
          </div>
        </div>
        <div v-if="cat3List.length">
          <div class="level-label">三级分类</div>
          <div class="level level-3">
            <div
              v-for="cat3 in cat3List"
              :key="cat3.id"
              :class="['cat-item', { active: cat3.name === selectedCat3 }]"
              @click="selectCat3(cat3.name)"
            >
              {{ cat3.name }}
            </div>
          </div>
        </div>
      </div>
      <div class="index-list" v-if="indexList.length">
        <div v-if="isLoadingIndices" class="loading-item">加载指标中...</div>
        <div
          v-for="idx in indexList"
          :key="idx.id"
          class="index-item"
          @click="$emit('select', {
            id: idx.id,
            name: idx.display_name,
            originalData: idx
          })"
        >
          <span>{{ idx.display_name }}</span>
        </div>
      </div>
      <div v-else class="empty-tip">
        <div v-if="isLoadingIndices">加载指标中...</div>
        <div v-else-if="!selectedCat1">请选择分类以查看指标</div>
        <div v-else>该分类下暂无可用指标</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { storeToRefs } from 'pinia'
import { useStates } from '../store/states'
import { fetchCategories, fetchIndicesByCategory } from '../utils/categoryApi'

const statesStore = useStates()
const {
  availableCategories,
  availableIndicators
} = storeToRefs(statesStore)

// Loading states
const isLoadingCategories = ref(false)
const isLoadingIndices = ref(false)

// Local selection state
const selectedCat1 = ref(null)
const selectedCat2 = ref(null)
const selectedCat3 = ref(null)

// Get granularity from store
const granularityComponent = computed(() => {
  return statesStore.states.find((state) => state.componentName === '粒度')
})

// Computed categories organized by levels
const categories = computed(() => {
  return availableCategories.value || []
})

const cat2List = computed(() => {
  const cat1 = categories.value.find((c) => c.name === selectedCat1.value)
  return cat1 ? cat1.children || [] : []
})

const cat3List = computed(() => {
  const cat2 = cat2List.value.find((c) => c.name === selectedCat2.value)
  return cat2 ? cat2.children || [] : []
})

const indexList = computed(() => {
  return availableIndicators.value || []
})

// Fetch categories when component mounts
onMounted(async () => {
  await loadCategories()
})

// Load categories from API
const loadCategories = async () => {
  const granularityId = granularityComponent.value?.granularityId
  if (!granularityId) {
    console.log('No granularity selected, skipping categories fetch')
    return
  }

  try {
    isLoadingCategories.value = true
    const categories = await fetchCategories(granularityId)
    statesStore.setAvailableCategories(categories)
    console.log('Categories loaded:', categories)
  } catch (error) {
    console.error('Failed to load categories:', error)
  } finally {
    isLoadingCategories.value = false
  }
}

// Load indices based on selected category
const loadIndices = async () => {
  const granularityId = granularityComponent.value?.granularityId
  if (!granularityId || !selectedCat1.value) {
    console.log('Missing granularity or category, skipping indices fetch')
    return
  }

  try {
    isLoadingIndices.value = true
    const indices = await fetchIndicesByCategory(
      granularityId,
      selectedCat1.value,
      selectedCat2.value || '',
      selectedCat3.value || ''
    )
    statesStore.setAvailableIndicators(indices)

    // Update selected categories in store
    statesStore.setSelectedCategories('indicators', {
      category_lv_1: selectedCat1.value,
      category_lv_2: selectedCat2.value || '',
      category_lv_3: selectedCat3.value || ''
    })

    console.log('Indices loaded for category:', {
      category_lv_1: selectedCat1.value,
      category_lv_2: selectedCat2.value,
      category_lv_3: selectedCat3.value,
      indices
    })
  } catch (error) {
    console.error('Failed to load indices:', error)
  } finally {
    isLoadingIndices.value = false
  }
}

function selectCat1(name) {
  selectedCat1.value = name
  selectedCat2.value = null
  selectedCat3.value = null
  // Clear indices when changing category
  statesStore.setAvailableIndicators([])
  // Load indices for the selected category
  loadIndices()
}

function selectCat2(name) {
  selectedCat2.value = name
  selectedCat3.value = null
  // Clear indices when changing category
  statesStore.setAvailableIndicators([])
  // Load indices for the selected category
  loadIndices()
}

function selectCat3(name) {
  selectedCat3.value = name
  // Clear indices when changing category
  statesStore.setAvailableIndicators([])
  // Load indices for the selected category
  loadIndices()
}

// Watch for granularity changes to reload categories
watch(() => granularityComponent.value?.granularityId, (newId, oldId) => {
  if (newId && newId !== oldId) {
    // Reset selections
    selectedCat1.value = null
    selectedCat2.value = null
    selectedCat3.value = null
    statesStore.setAvailableIndicators([])
    statesStore.clearSelectedCategories('indicators')
    // Load new categories
    loadCategories()
  }
})
</script>

<style scoped>
.index-selector {
  width: 100%;
  height: 100%;
  background: transparent;
  display: flex;
  flex-direction: column;
}
.selector-body {
  display: flex;
  flex-direction: row;
  flex: 1;
  padding: 20px;
  gap: 24px;
  overflow: hidden;
}
.category-levels {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 200px;
  max-width: 220px;
}
.level-label {
  font-size: 0.98rem;
  font-weight: 500;
  color: #1976d2;
  margin: 8px 0 4px 0;
  padding-left: 2px;
}
.level {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 4px;
}
.cat-item {
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  background: #f7faff;
  color: #333;
  transition:
    background 0.2s,
    color 0.2s;
}
.cat-item.active {
  background: #1976d2;
  color: #fff;
}
.index-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
  min-width: 200px;
}
.index-item {
  padding: 10px 16px;
  border-radius: 6px;
  background: #f5f5f5;
  cursor: pointer;
  font-size: 1rem;
  color: #1976d2;
  transition:
    background 0.2s,
    color 0.2s;
}
.index-item:hover {
  background: #1976d2;
  color: #fff;
}
.empty-tip {
  color: #aaa;
  font-size: 1rem;
  margin-top: 40px;
}
.loading-item {
  color: #1976d2;
  font-size: 0.9rem;
  padding: 8px 12px;
  text-align: center;
  font-style: italic;
}
.empty-item {
  color: #aaa;
  font-size: 0.9rem;
  padding: 8px 12px;
  text-align: center;
  font-style: italic;
}
</style>
