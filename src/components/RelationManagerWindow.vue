<template>
  <div class="relation-manager-mask">
    <div class="relation-manager-modal">
      <div class="relation-manager-header">
        <h2>关联关系管理</h2>
        <div style="display: flex; align-items: center; gap: 8px">
          <button
            class="collapse-btn"
            @click="collapsed = !collapsed"
            :title="collapsed ? '展开' : '收起'"
          >
            <span v-if="collapsed">▢</span>
            <span v-else>—</span>
          </button>
          <button class="close-btn" @click="$emit('close')">×</button>
        </div>
      </div>
      <div class="relation-manager-content" v-show="!collapsed">
        <section class="relation-section">
          <h3>粒度与指标/维度关联</h3>
          <p class="section-desc">设置不同粒度下可用的表和字段。</p>

          <!-- 1. Choose Target (粒度) -->
          <div class="form-row">
            <label for="granularity-select">选择粒度：</label>
            <select id="granularity-select" v-model="selectedGranularity">
              <option v-for="g in granularities" :key="g" :value="g">{{ g }}</option>
            </select>
          </div>

          <!-- 2. Choose Table -->
          <div class="form-row">
            <label for="table-select">选择表：</label>
            <select id="table-select" v-model="selectedTableKey">
              <option value="" disabled>请选择表</option>
              <option v-for="t in tableOptions" :key="t.value" :value="t.value">
                {{ t.label }}
              </option>
            </select>
          </div>

          <!-- 3. Choose Columns -->
          <div class="form-row" v-if="selectedTableKey">
            <label for="columns-select">选择字段：</label>
            <select
              id="columns-select"
              v-model="selectedColumns"
              multiple
              style="min-width: 200px; height: 80px"
            >
              <option v-for="col in columnOptions" :key="col.value" :value="col.value">
                {{ col.label }}
              </option>
            </select>
            <span style="margin-left: 8px; color: #888; font-size: 0.95em">（默认全选）</span>
          </div>

          <!-- 4. Mini-window: Current Config (当前配置) -->
          <div class="mini-config-window">
            <span class="mini-title">当前配置：</span>
            <template v-if="currentConfig.length === 0">
              <span class="empty">无</span>
            </template>
            <template v-else>
              <template v-if="!miniExpanded && currentConfig.length > 2">
                <span v-for="item in currentConfig.slice(0, 2)" :key="item" class="tag">{{
                  item
                }}</span>
                <span class="tag tag-ellipsis">... (共{{ currentConfig.length }}项)</span>
                <span class="mini-expand-link" @click="miniExpanded = true">展开</span>
              </template>
              <template v-else>
                <span v-for="item in currentConfig" :key="item" class="tag">{{ item }}</span>
                <span
                  v-if="currentConfig.length > 2"
                  class="mini-expand-link"
                  @click="miniExpanded = false"
                  >收起</span
                >
              </template>
            </template>
          </div>

          <!-- 5. Preview Change -->
          <div class="preview-window">
            <span class="mini-title">变更预览：</span>
            <template v-if="previewConfig.length === 0">
              <span class="empty">无</span>
            </template>
            <template v-else>
              <template v-if="!previewExpanded && previewConfig.length > 2">
                <span v-for="item in previewConfig.slice(0, 2)" :key="item" class="tag">{{
                  item
                }}</span>
                <span class="tag tag-ellipsis">... (共{{ previewConfig.length }}项)</span>
                <span class="mini-expand-link" @click="previewExpanded = true">展开</span>
              </template>
              <template v-else>
                <span v-for="item in previewConfig" :key="item" class="tag">{{ item }}</span>
                <span
                  v-if="previewConfig.length > 2"
                  class="mini-expand-link"
                  @click="previewExpanded = false"
                  >收起</span
                >
              </template>
            </template>
            <button class="save-btn" @click="saveConfig" :disabled="!isChanged">保存变更</button>
            <button class="reset-btn" @click="resetPreview" :disabled="!isChanged">重置</button>
          </div>

          <!-- 6. List of Stored Configs -->
          <div class="stored-configs">
            <span class="mini-title">所有配置：</span>
            <table>
              <thead>
                <tr>
                  <th>粒度</th>
                  <th>表</th>
                  <th>字段</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="conf in storedConfigs" :key="conf.granularity + '-' + conf.table">
                  <td>{{ conf.granularity }}</td>
                  <td>
                    {{ availableTables.find((t) => t.key === conf.table)?.name || conf.table }}
                  </td>
                  <td>{{ conf.columns.join(', ') || '无' }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </section>
        <section class="relation-section">
          <h3>指标与维度关联 (N:N)</h3>
          <p class="section-desc">选择一个指标，设置其可用的维度（多对多关系）。</p>
          <!-- Placeholder: future logic for selecting index and editing dimensions -->
          <div class="placeholder-box">此处将实现指标与维度的关联配置界面</div>
        </section>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useStates } from '@/store/states'

// Pinia store for tables/columns
const statesStore = useStates()
const tableState = computed(() => statesStore.states.find((s) => s.componentName === 'table'))
const sheetData = computed(() => tableState.value?.sheetData || {})
const availableTables = computed(() => {
  return Object.keys(sheetData.value).map((key) => ({
    key,
    name: sheetData.value[key].name || key,
    columns: sheetData.value[key].columns || [],
  }))
})

// Mocked data (granularities only)
const granularities = ['客户级', '全行级', '分行级', '借据号级', '产品级']

// Local state for configs
// Now: { granularity, table, columns: [...] }
const storedConfigs = ref([
  // Example initial config (empty)
])

const selectedGranularity = ref(granularities[0])
const selectedTableKey = ref('')
const selectedColumns = ref([])

// Collapse/minimize state
const collapsed = ref(false)
// Mini-window expand state
const miniExpanded = ref(false)
// Tag-list expand state
const tagListExpanded = ref(false)
// Preview-window expand state
const previewExpanded = ref(false)

// Get current config for selected granularity
const currentConfig = computed(() => {
  const found = storedConfigs.value.find(
    (c) => c.granularity === selectedGranularity.value && c.table === selectedTableKey.value,
  )
  return found ? found.columns : []
})

// Preview config (mutable before save)
const previewConfig = ref([])

// Watchers to update previewConfig
watch([selectedGranularity, selectedTableKey], ([newGran, newTable]) => {
  const found = storedConfigs.value.find((c) => c.granularity === newGran && c.table === newTable)
  if (newTable) {
    // Default: all columns in table
    const tableObj = availableTables.value.find((t) => t.key === newTable)
    previewConfig.value = found
      ? [...found.columns]
      : tableObj
        ? tableObj.columns.map((col) => col.title)
        : []
    selectedColumns.value = [...previewConfig.value]
  } else {
    previewConfig.value = []
    selectedColumns.value = []
  }
})

// Keep selectedColumns and previewConfig in sync
watch(selectedColumns, (cols) => {
  previewConfig.value = [...cols]
})

// Available tables and columns
const tableOptions = computed(() =>
  availableTables.value.map((t) => ({ value: t.key, label: t.name })),
)
const columnOptions = computed(() => {
  const tableObj = availableTables.value.find((t) => t.key === selectedTableKey.value)
  return tableObj ? tableObj.columns.map((col) => ({ value: col.title, label: col.title })) : []
})

const isChanged = computed(() => {
  return JSON.stringify(previewConfig.value) !== JSON.stringify(currentConfig.value)
})

function saveConfig() {
  const idx = storedConfigs.value.findIndex(
    (c) => c.granularity === selectedGranularity.value && c.table === selectedTableKey.value,
  )
  if (idx !== -1) {
    storedConfigs.value[idx].columns = [...previewConfig.value]
  } else {
    storedConfigs.value.push({
      granularity: selectedGranularity.value,
      table: selectedTableKey.value,
      columns: [...previewConfig.value],
    })
  }
}

function resetPreview() {
  previewConfig.value = [...currentConfig.value]
  selectedColumns.value = [...currentConfig.value]
}
</script>

<style scoped>
.relation-manager-mask {
  position: fixed;
  z-index: 2000;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.18);
  display: flex;
  align-items: center;
  justify-content: center;
}
.relation-manager-modal {
  background: #fff;
  border-radius: 18px;
  box-shadow:
    0 8px 32px rgba(25, 118, 210, 0.13),
    0 2px 8px rgba(0, 0, 0, 0.06);
  min-width: 1040px;
  max-width: 92vw;
  max-height: 92vh;
  overflow: hidden;
  padding: 0 0 32px 0;
  display: flex;
  flex-direction: column;
}
.relation-manager-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 28px 40px 16px 40px;
  border-bottom: 1.5px solid #e3f2fd;
}
.relation-manager-header h2 {
  font-size: 1.5rem;
  color: #1976d2;
  font-weight: bold;
  margin: 0;
  letter-spacing: 1px;
}
.close-btn {
  background: none;
  border: none;
  color: #888;
  font-size: 2.2rem;
  cursor: pointer;
  border-radius: 50%;
  padding: 0 14px;
  transition:
    background 0.2s,
    color 0.2s;
}
.close-btn:hover {
  background: #e3f2fd;
  color: #1976d2;
}
.relation-manager-content {
  overflow-y: auto;
  max-height: calc(92vh - 80px);
}
.relation-section {
  background: #f8fafd;
  border-radius: 14px;
  padding: 28px 28px 24px 28px;
  margin-bottom: 0;
  box-shadow: 0 2px 8px rgba(25, 118, 210, 0.04);
}
.relation-section h3 {
  color: #1976d2;
  font-size: 1.18rem;
  margin-bottom: 8px;
  font-weight: 600;
}
.section-desc {
  color: #666;
  font-size: 1.01rem;
  margin-bottom: 16px;
}
.placeholder-box {
  background: #f5f5f5;
  color: #aaa;
  border-radius: 6px;
  padding: 18px;
  text-align: center;
  font-size: 1.05rem;
  margin-top: 8px;
}
.form-row {
  margin-bottom: 18px;
  display: flex;
  align-items: center;
  gap: 12px;
}
.form-row label {
  font-weight: 500;
  color: #1976d2;
  font-size: 1.01rem;
}
.form-row select {
  padding: 7px 18px 7px 10px;
  border-radius: 6px;
  border: 1.5px solid #b3d1f7;
  background: #fff;
  font-size: 1.01rem;
  color: #1976d2;
  outline: none;
  transition: border 0.2s;
  box-shadow: 0 1px 2px rgba(25, 118, 210, 0.04);
}
.form-row select:focus {
  border: 1.5px solid #1976d2;
}
.mini-config-window {
  background: #eaf3fb;
  border-radius: 8px;
  padding: 12px 18px;
  margin-bottom: 14px;
  display: flex;
  align-items: center;
  gap: 10px;
  box-shadow: 0 1px 4px rgba(25, 118, 210, 0.06);
  min-height: 38px;
}
.mini-title {
  color: #1976d2;
  font-weight: bold;
  margin-right: 10px;
  font-size: 1.01rem;
}
.empty {
  color: #aaa;
  font-style: italic;
}
.tag {
  background: linear-gradient(90deg, #e3f2fd 60%, #f8fafd 100%);
  color: #1976d2;
  border-radius: 16px;
  padding: 4px 14px;
  margin-right: 6px;
  font-size: 1.01rem;
  display: inline-block;
  font-weight: 500;
  box-shadow: 0 1px 2px rgba(25, 118, 210, 0.06);
  transition:
    background 0.2s,
    color 0.2s;
}
.tag-removable {
  position: relative;
  margin-right: 10px;
  display: inline-flex;
  align-items: center;
}
.remove-btn {
  background: none;
  border: none;
  color: #888;
  font-size: 1.1rem;
  margin-left: 6px;
  cursor: pointer;
  border-radius: 50%;
  transition:
    background 0.2s,
    color 0.2s;
  padding: 0 4px;
}
.remove-btn:hover {
  background: #e3f2fd;
  color: #1976d2;
}
.tag-editor {
  margin-bottom: 14px;
  display: flex;
  flex-direction: column;
  gap: 6px;
}
.tag-editor label {
  font-weight: 500;
  color: #1976d2;
  font-size: 1.01rem;
}
.tag-editor select {
  padding: 7px 18px 7px 10px;
  border-radius: 6px;
  border: 1.5px solid #b3d1f7;
  background: #fff;
  font-size: 1.01rem;
  color: #1976d2;
  outline: none;
  transition: border 0.2s;
  box-shadow: 0 1px 2px rgba(25, 118, 210, 0.04);
}
.tag-editor select:focus {
  border: 1.5px solid #1976d2;
}
.tag-list {
  margin-top: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}
.preview-window {
  background: #f0f7fa;
  border-radius: 8px;
  padding: 12px 18px;
  margin-bottom: 14px;
  display: flex;
  align-items: center;
  gap: 10px;
  box-shadow: 0 1px 4px rgba(25, 118, 210, 0.06);
}
.save-btn,
.reset-btn {
  margin-left: 16px;
  padding: 6px 18px;
  border-radius: 6px;
  border: none;
  cursor: pointer;
  font-size: 1.01rem;
  font-weight: 500;
  box-shadow: 0 1px 2px rgba(25, 118, 210, 0.06);
  transition:
    background 0.2s,
    color 0.2s;
}
.save-btn {
  background: linear-gradient(90deg, #1976d2 80%, #63a4ff 100%);
  color: #fff;
}
.save-btn:disabled {
  background: #b3d1f7;
  color: #fff;
  cursor: not-allowed;
}
.reset-btn {
  background: #f5f5f5;
  color: #1976d2;
  border: 1px solid #e3f2fd;
}
.reset-btn:disabled {
  color: #aaa;
  border-color: #eee;
  cursor: not-allowed;
}
.stored-configs {
  margin-top: 16px;
}
.stored-configs .mini-title {
  display: block;
  margin-bottom: 8px;
}
.stored-configs table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  font-size: 1.01rem;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 4px rgba(25, 118, 210, 0.06);
}
.stored-configs th,
.stored-configs td {
  border-bottom: 1px solid #e3f2fd;
  padding: 10px 14px;
  text-align: left;
  color: #000;
}
.stored-configs th {
  background: #f8fafd;
  color: #1976d2;
  font-weight: 600;
}
.stored-configs tr:last-child td {
  border-bottom: none;
}
.collapse-btn {
  background: none;
  border: none;
  color: #888;
  font-size: 1.5rem;
  cursor: pointer;
  border-radius: 50%;
  padding: 0 8px;
  transition:
    background 0.2s,
    color 0.2s;
  margin-right: 2px;
}
.collapse-btn:hover {
  background: #e3f2fd;
  color: #1976d2;
}
.mini-collapse-btn {
  background: none;
  border: none;
  color: #888;
  font-size: 1.1rem;
  cursor: pointer;
  border-radius: 50%;
  padding: 0 6px;
  transition:
    background 0.2s,
    color 0.2s;
  margin-right: 2px;
}
.mini-collapse-btn:hover {
  background: #e3f2fd;
  color: #1976d2;
}
.tag-ellipsis {
  background: #fffbe6;
  color: #bfa100;
  border: 1px dashed #ffe58f;
  font-size: 0.98rem;
  margin-left: 4px;
}
.mini-expand-link {
  color: #1976d2;
  cursor: pointer;
  font-size: 0.98rem;
  margin-left: 8px;
  text-decoration: underline;
  user-select: none;
  transition: color 0.2s;
}
.mini-expand-link:hover {
  color: #125ea7;
}
</style>
