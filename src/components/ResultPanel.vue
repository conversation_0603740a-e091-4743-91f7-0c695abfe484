<template>
  <div class="resultpanel-root">
    <template v-if="resultsToShow.length">
      <div v-for="(res, idx) in resultsToShow" :key="idx" class="resultpanel-card">
        <div class="resultpanel-header">
          <div class="resultpanel-title">{{ res.title || '分析结果' }}</div>
          <div class="resultpanel-actions">
            <button class="resultpanel-action-btn" title="表格视图">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <rect
                  x="3"
                  y="5"
                  width="18"
                  height="14"
                  rx="2.5"
                  stroke="#4A90E2"
                  stroke-width="1.5"
                />
                <rect x="7" y="9" width="10" height="2" rx="1" fill="#4A90E2" />
                <rect x="7" y="13" width="6" height="2" rx="1" fill="#4A90E2" />
              </svg>
            </button>
            <button class="resultpanel-action-btn" title="图表视图">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <rect
                  x="4"
                  y="4"
                  width="16"
                  height="16"
                  rx="3"
                  stroke="#4A90E2"
                  stroke-width="1.5"
                />
                <rect x="7" y="13" width="2.5" height="3" rx="1.25" fill="#4A90E2" />
                <rect x="11" y="10" width="2.5" height="6" rx="1.25" fill="#4A90E2" />
                <rect x="15" y="7" width="2.5" height="9" rx="1.25" fill="#4A90E2" />
              </svg>
            </button>
          </div>
        </div>
        <div class="resultpanel-content">
          <div v-if="res.chart" class="resultpanel-chart-placeholder">
            <!-- Placeholder for chart, can be replaced with real chart -->
            <img
              src="https://placehold.co/320x120/edf3fa/4A90E2?text=Chart"
              alt="Chart"
              style="width: 100%; border-radius: 8px"
            />
          </div>
          <div v-else class="resultpanel-summary">
            <pre>{{ formatResult(res.data || res) }}</pre>
          </div>
        </div>
      </div>
    </template>
    <div v-else class="resultpanel-placeholder">
      <div class="resultpanel-icon">📊</div>
      <div class="resultpanel-tip">分析结果将在这里展示</div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
const props = defineProps({
  results: { type: Array, default: null },
  result: { type: [Object, Array, String], default: null },
})
const resultsToShow = computed(() => {
  if (props.results && props.results.length) return props.results
  if (props.result) return [props.result]
  return []
})
const formatResult = (r) => {
  if (typeof r === 'string') return r
  try {
    return JSON.stringify(r, null, 2)
  } catch {
    return String(r)
  }
}
</script>

<style scoped>
.resultpanel-root {
  width: 100%;
  height: 100%;
  min-height: calc(100vh - 120px);
  background: #f7faff;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 36px 0 36px 0;
  overflow-y: auto;
}
.resultpanel-card {
  background: #fff;
  border-radius: 18px;
  box-shadow: 0 4px 24px rgba(80, 120, 200, 0.08);
  padding: 2.2rem 2rem 1.5rem 2rem;
  min-width: 340px;
  max-width: 95vw;
  width: 420px;
  margin-bottom: 32px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.resultpanel-header {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.2rem;
}
.resultpanel-title {
  font-size: 1.18rem;
  font-weight: 600;
  color: #2a2a2a;
}
.resultpanel-actions {
  display: flex;
  gap: 8px;
}
.resultpanel-action-btn {
  background: #f7faff;
  border: 1.5px solid #e0e6f0;
  border-radius: 8px;
  padding: 4px 7px;
  cursor: pointer;
  transition:
    border 0.2s,
    background 0.2s;
  display: flex;
  align-items: center;
}
.resultpanel-action-btn:hover {
  border: 1.5px solid #4a90e2;
  background: #eaf6ff;
}
.resultpanel-content {
  width: 100%;
  min-height: 80px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.resultpanel-chart-placeholder {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
}
.resultpanel-summary {
  width: 100%;
  color: #3a3a3a;
  font-size: 1rem;
  background: #f5f6fa;
  border-radius: 8px;
  padding: 1rem;
  margin-top: 0.5rem;
  overflow-x: auto;
}
.resultpanel-placeholder {
  text-align: center;
  color: #b0b8c9;
  margin-top: 80px;
}
.resultpanel-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}
.resultpanel-tip {
  font-size: 1.2rem;
}
</style>
