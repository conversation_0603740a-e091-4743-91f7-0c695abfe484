<template>
  <div class="chart-module">
    <div class="chart-title">📈 趋势分析结果图</div>
    <VChart :option="chartOptions" autoresize style="width: 100%; height: 300px" />
  </div>
</template>

<script setup>
import { use } from 'echarts/core'
import VChart from 'vue-echarts'
import { LineChart } from 'echarts/charts'
import {
  GridComponent,
  TooltipComponent,
  LegendComponent,
  TitleComponent,
} from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'

use([LineChart, GridComponent, TooltipComponent, LegendComponent, TitleComponent, CanvasRenderer])

const props = defineProps({
  chartOptions: {
    type: Object,
    required: true,
  },
})
</script>

<style scoped>
.chart-module {
  width: 100%;
  min-height: 120px;
  background: #f9f9fb;
  border-radius: 10px;
  padding: 0.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.chart-title {
  font-weight: bold;
  color: #1976d2;
  margin-bottom: 0.5rem;
  text-align: center;
}
</style>
