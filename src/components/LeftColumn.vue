<template>
  <div class="analysis-object-container">
    <div class="left-title-bar">
      <span>分析对象</span>
      <div style="display: flex; gap: 1px; align-items: center">
        <span class="upload-doc-icon" @click="triggerFileInput" title="上传文档">
          <svg width="20" height="20" viewBox="0 0 26 26" fill="none">
            <path
              d="M16.5 6.5l-7.8 7.8a3 3 0 1 0 4.2 4.2l7.1-7.1a5 5 0 0 0-7.1-7.1l-8.5 8.5a7 7 0 0 0 9.9 9.9l8.5-8.5"
              stroke="#1976d2"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              fill="none"
            />
          </svg>
          <input
            ref="fileInput"
            type="file"
            accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
            @change="handleFileUpload"
            style="display: none"
          />
        </span>
        <!-- <button class="table-process-btn" @click="emit('goto-datasheet')">智能数据切片</button> -->
      </div>
    </div>

    <!-- 粒度卡片 -->
    <div class="analysis-card" :class="{ 'card-inactive': !isGranularityActive }">
      <div
        class="card-header"
        style="display: flex; align-items: center; justify-content: space-between"
      >
        <span class="card-icon">📊</span>
        <span class="card-title main">粒度</span>
        <div class="granularity-selector-container">
          <button
            class="granularity-selector-btn"
            @click="isGranularityActive ? toggleGranularitySelector() : null"
            :disabled="!isGranularityActive"
          >
            {{ selectedGranularity || '选择粒度' }}
            <span class="dropdown-arrow">▼</span>
          </button>

          <!-- Granularity Selector Dropdown -->
          <div v-if="showGranularitySelector" class="granularity-dropdown">
            <div class="granularity-list">
              <div
                v-for="granularity in availableGranularities"
                :key="granularity.id"
                :class="[
                  'granularity-item',
                  {
                    active: selectedGranularity === granularity.display_name,
                    disabled: !granularity.is_permitted || granularity.is_permitted === 0,
                  },
                ]"
                @mousedown.prevent="
                  granularity.is_permitted === true || granularity.is_permitted === 1
                    ? handleGranularitySelect(granularity)
                    : console.log('Granularity not permitted:', granularity)
                "
              >
                {{ granularity.display_name }}
              </div>
              <div v-if="isLoadingGranularities" class="loading-item">加载中...</div>
              <div
                v-if="!isLoadingGranularities && availableGranularities.length === 0"
                class="empty-item"
              >
                暂无可用粒度
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 日期卡片 -->
    <div class="analysis-card" :class="{ 'card-inactive': !isDateActive }">
      <div
        class="card-header"
        style="display: flex; align-items: center; justify-content: space-between"
      >
        <div style="display: flex; align-items: center; gap: 0.4rem">
          <span class="card-icon">📅</span>
          <span class="card-title main">日期</span>
        </div>
        <div class="date-mode-switcher">
          <button
            :class="['date-mode-btn', { active: dateMode === 'range' }]"
            @click="isDateActive ? (dateMode = 'range') : null"
            type="button"
            :disabled="!isDateActive"
          >
            区间
          </button>
          <button
            :class="['date-mode-btn', { active: dateMode === 'multiple' }]"
            @click="isDateActive ? (dateMode = 'multiple') : null"
            type="button"
            :disabled="!isDateActive"
          >
            多选
          </button>
        </div>
      </div>
      <div class="date-list">
        <div class="date-row" style="display: flex; align-items: center; width: 100%; gap: 0.7rem">
          <span class="date-label" style="margin-bottom: 0">日期：</span>
          <div style="flex: 1; min-width: 180px">
            <DateSelector
              v-if="dateMode === 'range'"
              v-model="dateRange"
              mode="range"
              placeholder="选择日期范围"
              class="date-input"
              :disabled="!isDateActive"
            />
            <DateSelector
              v-else
              v-model="multiDates"
              mode="multiple"
              placeholder="多选日期"
              class="date-input"
              :disabled="!isDateActive"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 目标指标卡片 -->
    <div v-if="targetIndicators.length > 0">
      <div
        class="analysis-card"
        v-for="indicator in targetIndicators"
        :key="indicator.componentId"
        :class="{ 'card-inactive': !isIndicatorActive }"
      >
        <div class="card-header">
          <span class="card-icon">🎯</span>
          <span class="card-title main">目标指标</span>
          <div class="indicator-selector-dropdown">
            <select
              class="indicator-selector"
              v-model="newIndicatorName"
              @mousedown.prevent="emit('add-index')"
              :disabled="!isIndicatorActive"
            >
              <option value="" disabled selected>＋ 添加指标</option>
              <option
                v-for="indicatorOption in availableIndicators.length > 0
                  ? availableIndicators
                  : ['test1', 'test2', 'test3']"
                :key="indicatorOption"
                :value="indicatorOption"
              >
                {{ indicatorOption }}
              </option>
            </select>
          </div>
        </div>
        <div class="indicator-list">
          <div class="indicator-tabs">
            <template v-if="!showAllIndicators">
              <template
                v-for="(index, idx) in indicator.indices.slice(0, 4)"
                :key="getIndexKey(index)"
              >
                <span class="indicator-tab">
                  {{ getIndexDisplayName(index) }}
                  <button class="indicator-remove-btn" @click="removeIndicator(indicator, idx)">
                    ×
                  </button>
                </span>
              </template>
              <template v-if="indicator.indices.length > 4">
                <span class="indicator-tab expand-tab" @click="showAllIndicators = true">
                  +{{ indicator.indices.length - 4 }} 展开
                </span>
              </template>
            </template>
            <template v-else>
              <template v-for="(index, idx) in indicator.indices" :key="getIndexKey(index)">
                <span class="indicator-tab">
                  {{ getIndexDisplayName(index) }}
                  <button class="indicator-remove-btn" @click="removeIndicator(indicator, idx)">
                    ×
                  </button>
                </span>
              </template>
              <span class="indicator-tab expand-tab" @click="showAllIndicators = false">收起</span>
            </template>
          </div>
        </div>
      </div>
    </div>

    <!-- 目标维度卡片 -->
    <div v-if="targetDimensions.length > 0">
      <div
        class="analysis-card"
        v-for="dimension in targetDimensions"
        :key="dimension.componentId"
        :class="{ 'card-inactive': !isDimensionActive }"
      >
        <div class="card-header">
          <span class="card-icon">📐</span>
          <span class="card-title main">目标维度</span>
          <div class="indicator-selector-dropdown">
            <button
              class="indicator-selector"
              @click="isDimensionActive ? handleAddDimensionClick() : null"
              :disabled="!isDimensionActive"
            >
              ＋ 添加维度
            </button>
          </div>
        </div>
        <div class="indicator-list">
          <div class="indicator-tabs">
            <template v-if="!showAllDimensions">
              <template
                v-for="(dim, idx) in dimension.dimensions.slice(0, 4)"
                :key="getDimensionKey(dim)"
              >
                <span class="indicator-tab">
                  {{ getDimensionDisplayName(dim) }}
                  <button class="indicator-remove-btn" @click="removeDimension(dimension, idx)">
                    ×
                  </button>
                </span>
              </template>
              <template v-if="dimension.dimensions.length > 4">
                <span class="indicator-tab expand-tab" @click="showAllDimensions = true">
                  +{{ dimension.dimensions.length - 4 }} 展开
                </span>
              </template>
            </template>
            <template v-else>
              <template v-for="(dim, idx) in dimension.dimensions" :key="getDimensionKey(dim)">
                <span class="indicator-tab">
                  {{ getDimensionDisplayName(dim) }}
                  <button class="indicator-remove-btn" @click="removeDimension(dimension, idx)">
                    ×
                  </button>
                </span>
              </template>
              <span class="indicator-tab expand-tab" @click="showAllDimensions = false">收起</span>
            </template>
          </div>
        </div>
      </div>
    </div>

    <!-- 筛选卡片 -->
    <div class="analysis-card" :class="{ 'card-inactive': !isFilterActive }">
      <div class="card-header">
        <span class="card-icon">🔍</span>
        <span class="card-title main">筛选</span>
        <span
          class="filter-help-icon"
          @click="isFilterActive ? (showFilterHelp = true) : null"
          title="如何使用分组筛选？"
          >💡</span
        >
        <button
          class="card-add-btn"
          @click="isFilterActive ? addFilterGroup() : null"
          :disabled="!isFilterActive"
        >
          +
        </button>
      </div>
      <!-- Help Popover -->
      <div v-if="showFilterHelp" class="filter-help-popover">
        <div class="filter-help-popover-header">
          <span>如何使用分组筛选？</span>
          <button class="filter-help-close-btn" @click="showFilterHelp = false">×</button>
        </div>
        <div class="filter-help-popover-content">
          <p><strong>示例：</strong></p>
          <ul>
            <li>
              <strong>A AND (B OR C)</strong>：创建两个条件组，第一组放A，第二组放B和C（用OR连接）
            </li>
            <li>
              <strong>(A OR B) AND (C OR D)</strong>：创建两个条件组，每组包含两个用OR连接的条件
            </li>
            <li><strong>A AND B AND C</strong>：可以放在一个组内，用AND连接</li>
          </ul>
          <p><strong>操作说明：</strong></p>
          <ul>
            <li>点击"+"添加新的条件组</li>
            <li>在组内点击"+ 添加条件"添加更多条件</li>
            <li>每个条件包含：字段选择器 + 操作符 + 值输入框</li>
            <li>选择AND/OR连接符控制逻辑关系</li>
            <li>系统会自动应用筛选条件</li>
          </ul>
        </div>
      </div>

      <div class="filter-list">
        <!-- Filter Groups -->
        <div v-if="filterGroups.length === 0" class="empty-filters">
          <p>暂无筛选条件</p>
          <p class="empty-hint">点击右上角"+"添加筛选条件</p>
        </div>

        <div class="filter-group" v-for="(group, groupIndex) in filterGroups" :key="groupIndex">
          <div class="group-header">
            <span class="group-label">条件组 {{ groupIndex + 1 }}</span>
            <button
              class="group-remove-btn"
              @click="removeFilterGroup(groupIndex)"
              v-if="filterGroups.length > 1"
            >
              ×
            </button>
          </div>
          <!-- Conditions within this group -->
          <div class="group-conditions">
            <div
              class="filter-item"
              v-for="(filter, filterIndex) in group.conditions"
              :key="filterIndex"
            >
              <div
                v-if="filter._showExpression && filter.field && filter.operator && filter.value"
                class="filter-expression-row"
              >
                <span class="filter-expression-chip">
                  {{ filter.field }} {{ operatorToChinese[filter.operator] || filter.operator }}
                  <template v-if="filter.operator === 'IN' || filter.operator === 'NOT IN'">
                    {{
                      Array.isArray(filter.value)
                        ? filter.value.filter((v) => v).join('、')
                        : filter.value
                    }}
                  </template>
                  <template v-else>
                    {{ filter.value }}
                  </template>
                </span>
                <button
                  class="edit-condition-btn"
                  @click="toggleConditionEdit(groupIndex, filterIndex)"
                  title="编辑"
                >
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <path d="M4 21h17" stroke="#1976d2" stroke-width="2" stroke-linecap="round" />
                    <path
                      d="M12.5 6.5l5 5L7 22H2v-5l10.5-10.5z"
                      stroke="#1976d2"
                      stroke-width="2"
                      stroke-linecap="round"
                    />
                  </svg>
                </button>
                <!-- Logic selector in expression mode -->
                <div
                  v-if="filterIndex < group.conditions.length - 1"
                  class="logic-selector-inline logic-selector-expression"
                >
                  <select class="logic-select" v-model="filter.logic" @change="handleFilterChange">
                    <option value="AND">且</option>
                    <option value="OR">或</option>
                  </select>
                </div>
              </div>
              <div v-else>
                <div class="filter-label-row">
                  <span class="filter-label">条件：</span>
                  <button
                    class="filter-remove-btn"
                    @click="removeFilterCondition(groupIndex, filterIndex)"
                    v-if="group.conditions.length > 1"
                  >
                    ×
                  </button>
                </div>
                <div class="condition-parts">
                  <!-- Field selector (indicator/dimension) -->
                  <select class="field-select" v-model="filter.field" @change="handleFilterChange">
                    <option value="">选择字段</option>
                    <optgroup label="指标">
                      <option
                        v-for="indicator in availableIndicators"
                        :key="indicator.id"
                        :value="indicator.display_name"
                      >
                        {{ indicator.display_name }}
                      </option>
                    </optgroup>
                    <optgroup label="维度">
                      <option
                        v-for="dimension in availableDimensions"
                        :key="dimension.id"
                        :value="dimension.display_name"
                      >
                        {{ dimension.display_name }}
                      </option>
                    </optgroup>
                  </select>
                  <!-- Operator selector -->
                  <select
                    class="operator-select"
                    v-model="filter.operator"
                    @change="onOperatorChange(filter)"
                  >
                    <option value="">操作符</option>
                    <option value="=">等于</option>
                    <option value="!=">不等于</option>
                    <option value=">">大于</option>
                    <option value=">=">大于等于</option>
                    <option value="<">小于</option>
                    <option value="<=">小于等于</option>
                    <option value="LIKE">包含</option>
                    <option value="NOT LIKE">不包含</option>
                    <option value="IN">在列表中</option>
                    <option value="NOT IN">不在列表中</option>
                  </select>
                  <!-- Value input -->
                  <template v-if="filter.operator === 'IN' || filter.operator === 'NOT IN'">
                    <div class="multi-value-inputs">
                      <span v-for="(val, idx) in filter.value" :key="idx" class="multi-input-item">
                        <input
                          class="multi-value-input"
                          v-model="filter.value[idx]"
                          @input="handleFilterChange"
                          placeholder="输入值"
                        />
                        <button
                          class="remove-value-btn"
                          @click="removeValue(filter, idx)"
                          v-if="filter.value.length > 1"
                          title="删除"
                        >
                          ×
                        </button>
                      </span>
                      <button class="add-value-btn" @click="addValue(filter)" title="添加值">
                        +
                      </button>
                    </div>
                  </template>
                  <template v-else>
                    <input
                      class="value-input"
                      v-model="filter.value"
                      @input="handleFilterChange"
                      placeholder="输入值"
                    />
                  </template>
                  <!-- Logic selector between conditions within the same group -->
                  <div
                    v-if="filterIndex < group.conditions.length - 1"
                    class="logic-selector-inline"
                  >
                    <select
                      class="logic-select"
                      v-model="filter.logic"
                      @change="handleFilterChange"
                    >
                      <option value="AND">且</option>
                      <option value="OR">或</option>
                    </select>
                  </div>
                </div>
                <div
                  v-if="filter.field && filter.operator && filter.value"
                  class="to-expression-btn-row"
                >
                  <button
                    class="to-expression-btn"
                    @click="toggleConditionExpression(groupIndex, filterIndex)"
                  >
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                      <circle cx="12" cy="12" r="10" stroke="#42b983" stroke-width="2" />
                      <path d="M8 12h8" stroke="#42b983" stroke-width="2" stroke-linecap="round" />
                    </svg>
                    转为表达式
                  </button>
                </div>
              </div>
            </div>
            <!-- Add condition button within group and group logic selector -->
            <div class="add-condition-group-row">
              <button class="add-condition-btn" @click="addFilterCondition(groupIndex)">
                + 添加条件
              </button>
              <div v-if="groupIndex < filterGroups.length - 1" class="group-logic-selector-inline">
                <select
                  class="group-logic-select"
                  v-model="group.groupLogic"
                  @change="handleFilterChange"
                >
                  <option value="AND">且</option>
                  <option value="OR">或</option>
                </select>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import axios from 'axios'
import { computed, ref, onMounted, onUnmounted, watch } from 'vue'
import { storeToRefs } from 'pinia'
import { useStates } from '../store/states'
import DateSelector from './common/DateSelector.vue'
import { v4 as uuidv4 } from 'uuid'
import { importAndAddSheet } from '@/composables/useSheetImport'
import { handleError } from '@/utils/errorHandler'

const emit = defineEmits(['goto-datasheet', 'add-index', 'add-dimension', 'file-uploaded'])

const statesStore = useStates()
const { updateComponent, setAvailableIndicators, setAvailableDimensions } = statesStore
const { states, authToken, availableIndicators, availableDimensions } = storeToRefs(statesStore)

const isLoadingIndicators = ref(false)
const isLoadingDimensions = ref(false)

const targetIndicators = computed(() => {
  return states.value.filter((state) => state.componentName === '目标指标')
})

const targetDimensions = computed(() => {
  return states.value.filter((state) => state.componentName === '目标维度')
})

const granularityComponent = computed(() => {
  return states.value.find((state) => state.componentName === '粒度')
})

const selectedDimensions = ref([])

// Granularity state management
const availableGranularities = ref([])
const selectedGranularity = ref('')
const selectedGranularityId = ref('')
const selectedGranularityName = ref('')
const isLoadingGranularities = ref(false)
const showGranularitySelector = ref(false)

// Get filter component from store
const filterComponent = computed(() => {
  return states.value.find((state) => state.componentName === '筛选')
})

// Get filterGroups from store with reactive reference
const filterGroups = computed({
  get: () => {
    if (!filterComponent.value) {
      return []
    }
    return filterComponent.value.filterGroups || []
  },
  set: (newValue) => {
    if (filterComponent.value) {
      updateComponent(filterComponent.value.componentId, {
        ...filterComponent.value,
        filterGroups: newValue,
      })
    } else {
      // Create new filter component if it doesn't exist
      const newFilterComponent = {
        componentId: uuidv4(),
        componentName: '筛选',
        filterGroups: newValue,
      }
      updateComponent(newFilterComponent.componentId, newFilterComponent)
    }
  },
})

// Help section state
const showFilterHelp = ref(false)

// Fetch indicators function that can be reused
const fetchIndicators = async () => {
  if (isLoadingIndicators.value) {
    return
  }

  // Check if we have granularity selected
  const granularityComponent = states.value.find((state) => state.componentName === '粒度')
  if (!granularityComponent?.granularity) {
    console.log('No granularity selected, skipping indicators fetch')
    return
  }

  try {
    isLoadingIndicators.value = true
    const params = {
      key: granularityComponent.granularityId,
      category_lv_1: '总公司', // Default category for now
    }

    console.log('Fetching indicators with params:', params)
    const resp = await axios.get('/api/v1/config/data_prep/indices', {
      params,
      headers: {
        token: localStorage.getItem('token'),
      },
    })
    console.log('Indicators API response:', resp.data)

    if (resp.data.error_code === 0) {
      // Store complete response data including all indicator metadata and dimension information
      setAvailableIndicators(resp.data.data || [])
      console.log('Stored complete indicator data:', resp.data.data)
    } else {
      console.error('API error:', resp.data.message)
      // Use mock data for development
      setAvailableIndicators([
        {
          id: '101',
          name: 'overdue_amount',
          display_name: '逾期金额',
          category_lv_1: '个人信贷部',
          category_lv_2: '信用贷款处',
          category_lv_3: '逾期类',
          is_permission: 1,
        },
        {
          id: '102',
          name: 'bill_amount',
          display_name: '账单金额',
          category_lv_1: '个人信贷部',
          category_lv_2: '信用贷款处',
          category_lv_3: '账单类',
          is_permission: 1,
        },
      ])
    }
  } catch (error) {
    console.error('Failed to fetch indices:', error)

    // Use the new error handler to classify and display errors appropriately
    const retryAction = () => fetchIndicators() // Allow retry for system errors
    handleError(error, '获取指标数据失败', retryAction)

    // Use mock data for development/network errors as fallback
    setAvailableIndicators([
      {
        id: '101',
        name: 'overdue_amount',
        display_name: '逾期金额',
        category_lv_1: '个人信贷部',
        category_lv_2: '信用贷款处',
        category_lv_3: '逾期类',
        is_permitted: 1,
      },
      {
        id: '102',
        name: 'bill_amount',
        display_name: '账单金额',
        category_lv_1: '个人信贷部',
        category_lv_2: '信用贷款处',
        category_lv_3: '账单类',
        is_permitted: 1,
      },
    ])
  } finally {
    isLoadingIndicators.value = false
  }
}

// Fetch dimensions function that can be reused
const fetchDimensions = async () => {
  if (isLoadingDimensions.value) {
    return
  }

  // Check if we have granularity selected and indicators
  const granularityComponent = states.value.find((state) => state.componentName === '粒度')
  const indicatorComponent = states.value.find((state) => state.componentName === '目标指标')

  if (!granularityComponent?.granularityId) {
    console.log('No granularity selected, skipping dimensions fetch')
    return
  }

  try {
    isLoadingDimensions.value = true
    const params = {
      key: granularityComponent.granularityId,
      category_lv_1: '总公司', // Default category for now
      indices: indicatorComponent?.indices?.map((idx) => getIndexKey(idx)) || [],
    }

    console.log('Fetching dimensions with params:', params)
    const resp = await axios.get('/api/v1/config/data_prep/dimensions', {
      params,
      headers: {
        token: localStorage.getItem('token'),
      },
    })
    console.log('Dimensions API response:', resp.data)

    if (resp.data.error_code === 0) {
      // Store complete response data including all dimension metadata
      setAvailableDimensions(resp.data.data || [])
      console.log('Stored complete dimension data:', resp.data.data)
    } else {
      console.error('API error:', resp.data.message)
      // Use mock data for development
      setAvailableDimensions([
        {
          id: '101',
          name: 'region',
          display_name: '地区',
          category_lv_1: '个人信贷部',
          category_lv_2: '信用贷款处',
          category_lv_3: '逾期类',
          is_permission: 1,
        },
        {
          id: '102',
          name: 'branch',
          display_name: '所属分行',
          category_lv_1: '个人信贷部',
          category_lv_2: '信用贷款处',
          category_lv_3: '分行类',
          is_permission: 1,
        },
      ])
    }
  } catch (error) {
    console.error('Failed to fetch dimensions:', error)
    // Use mock data for development/network errors
    setAvailableDimensions([
      {
        id: '101',
        name: 'region',
        display_name: '地区',
        category_lv_1: '个人信贷部',
        category_lv_2: '信用贷款处',
        category_lv_3: '逾期类',
        is_permission: 1,
      },
      {
        id: '102',
        name: 'branch',
        display_name: '所属分行',
        category_lv_1: '个人信贷部',
        category_lv_2: '信用贷款处',
        category_lv_3: '分行类',
        is_permission: 1,
      },
    ])
  } finally {
    isLoadingDimensions.value = false
  }
}

// Fetch all granularity options
const fetchGranularityKeys = async () => {
  try {
    const response = await axios.get('/api/v1/config/data_prep/keys', {
      headers: {
        token: localStorage.getItem('token'),
      },
    })
    console.log(response)
    if (response.data.error_code === 0) {
      return response.data.data || []
    } else {
      console.error('API error:', response.data.message)
      // Return mock data when API returns error
      return [
        { id: '1', name: 'cust', display_name: '客户级', is_permitted: true },
        { id: '2', name: 'account', display_name: '账户级', is_permitted: true },
        { id: '3', name: 'branch', display_name: '分行级', is_permitted: false },
      ]
    }
  } catch (error) {
    console.error('Failed to fetch granularity keys:', error)
    // Return mock data for development/network errors
    return [
      { id: '1', name: 'cust', display_name: '客户级', is_permitted: true },
      { id: '2', name: 'account', display_name: '账户级', is_permitted: true },
      { id: '3', name: 'branch', display_name: '分行级', is_permitted: false },
    ]
  }
}

// Fetch indicators when component mounts
onMounted(() => {
  const initStateDimension = targetDimensions.value[0]
  selectedDimensions.value = initStateDimension.dimensions

  const initGranularityComponent = granularityComponent.value
  selectedGranularity.value = initGranularityComponent?.granularity || ''
  selectedGranularityId.value = initGranularityComponent?.granularityId || ''
  selectedGranularityName.value = initGranularityComponent?.granularityName || ''

  fetchIndicators()
  fetchDimensions()

  // Add click outside handler for granularity dropdown
  document.addEventListener('click', handleClickOutside)
})

// Cleanup event listener
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

// Handle click outside granularity dropdown
const handleClickOutside = (event) => {
  // Small delay to prevent race condition with mousedown events
  setTimeout(() => {
    // Check if click is inside granularity selector container
    const granularityContainer = event.target.closest('.granularity-selector-container')
    const granularityDropdown = event.target.closest('.granularity-dropdown')

    // If click is outside both the container and dropdown, close the dropdown
    if (!granularityContainer && !granularityDropdown && showGranularitySelector.value) {
      console.log('Clicking outside granularity selector, closing dropdown')
      showGranularitySelector.value = false
    }
  }, 10)
}

const handleDimensionSelectClick = () => {
  const component = targetDimensions.value[0]
  const newComponent = {
    ...component,
    dimensions: selectedDimensions.value,
  }
  updateComponent(component.componentId, newComponent)
}

// Watch for changes in selectedDimensions to update the store
watch(selectedDimensions, handleDimensionSelectClick)

// Watch for changes in granularity component to keep local state in sync
watch(
  granularityComponent,
  (newComponent) => {
    console.log('Granularity component changed:', newComponent)
    if (newComponent) {
      selectedGranularity.value = newComponent.granularity || ''
      selectedGranularityId.value = newComponent.granularityId || ''
      selectedGranularityName.value = newComponent.granularityName || ''
      console.log('Updated local state from component:', {
        selectedGranularity: selectedGranularity.value,
        selectedGranularityId: selectedGranularityId.value,
        selectedGranularityName: selectedGranularityName.value,
      })
    }
  },
  { immediate: true },
)

// Watch for changes in selectedGranularity to debug
watch(selectedGranularity, (newValue, oldValue) => {
  console.log('selectedGranularity changed from', oldValue, 'to', newValue)
  // Fetch indicators when granularity changes
  if (newValue && newValue !== oldValue) {
    fetchIndicators()
    // Also fetch dimensions when granularity changes
    fetchDimensions()
  }
})

// Watch for changes in target indicators to refetch dimensions
watch(
  targetIndicators,
  (newIndicators, oldIndicators) => {
    // Only refetch if the indicators actually changed and we have a granularity selected
    if (
      selectedGranularity.value &&
      JSON.stringify(newIndicators) !== JSON.stringify(oldIndicators)
    ) {
      fetchDimensions()
    }
  },
  { deep: true },
)

const handleFilterChange = () => {
  // The filterGroups computed property automatically updates the store
  // No need to manually call updateComponent here
}

const addFilterCondition = (groupIndex) => {
  const newFilterGroups = [...filterGroups.value]
  newFilterGroups[groupIndex].conditions.push({
    field: '',
    operator: '',
    value: '',
    logic: 'AND',
  })
  filterGroups.value = newFilterGroups
}

const removeFilterCondition = (groupIndex, filterIndex) => {
  if (filterGroups.value[groupIndex].conditions.length > 1) {
    const newFilterGroups = [...filterGroups.value]
    newFilterGroups[groupIndex].conditions.splice(filterIndex, 1)
    filterGroups.value = newFilterGroups
  }
}

const dateComponent = computed(() => {
  return states.value.find((state) => state.componentName === '日期')
})

// Date utility functions for yyyymmdd format
const parseYYYYMMDD = (dateStr) => {
  if (!dateStr || typeof dateStr !== 'string' || dateStr.length !== 8) {
    return null
  }
  const year = parseInt(dateStr.substring(0, 4), 10)
  const month = parseInt(dateStr.substring(4, 6), 10) - 1 // Month is 0-indexed
  const day = parseInt(dateStr.substring(6, 8), 10)
  const date = new Date(year, month, day)
  // Validate the date
  if (date.getFullYear() !== year || date.getMonth() !== month || date.getDate() !== day) {
    return null
  }
  return date
}

const formatToYYYYMMDD = (date) => {
  if (!date || !(date instanceof Date) || isNaN(date.getTime())) {
    return null
  }
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}${month}${day}`
}

const dateMode = ref('range')
const dateRange = computed({
  get() {
    const comp = dateComponent.value
    if (comp && Array.isArray(comp.dates) && comp.dates.length > 0) {
      // Parse yyyymmdd format dates and return as [start, end] for DateSelector
      const parsedDates = comp.dates.map((d) => parseYYYYMMDD(d)).filter((date) => date !== null)
      if (parsedDates.length > 0) {
        const sorted = parsedDates.sort((a, b) => a - b)
        return [sorted[0], sorted[sorted.length - 1]]
      }
    }
    return []
  },
  set(val) {
    const comp = dateComponent.value
    if (comp) {
      if (Array.isArray(val) && val.length === 2 && val[0] && val[1]) {
        // Generate all dates between start and end (inclusive)
        const start = new Date(val[0])
        const end = new Date(val[1])
        const dates = []
        let current = new Date(start)
        while (current <= end) {
          // Format as yyyymmdd using utility function
          const formattedDate = formatToYYYYMMDD(current)
          if (formattedDate) {
            dates.push(formattedDate)
          }
          current.setDate(current.getDate() + 1)
        }
        updateComponent(comp.componentId, {
          ...comp,
          dates,
        })
      } else if (Array.isArray(val) && val.length === 0) {
        // Handle clear
        updateComponent(comp.componentId, {
          ...comp,
          dates: [],
        })
      }
    }
  },
})

const multiDates = computed({
  get() {
    const comp = dateComponent.value
    if (comp && Array.isArray(comp.dates) && comp.dates.length > 0) {
      // Return dates in yyyymmdd format for DateSelector multiple mode
      return comp.dates.filter((d) => d && typeof d === 'string' && d.length === 8)
    }
    return []
  },
  set(newDates) {
    if (dateMode.value === 'multiple') {
      const comp = dateComponent.value
      if (comp) {
        updateComponent(comp.componentId, {
          ...comp,
          dates: Array.isArray(newDates) ? newDates : [],
        })
      }
    }
  },
})

// Clear dates when switching between modes
watch(dateMode, (mode) => {
  const comp = dateComponent.value
  if (comp) {
    // Clear dates when switching modes to avoid confusion
    updateComponent(comp.componentId, { ...comp, dates: [] })
  }
})

const newIndicatorName = ref('')

const addFilterGroup = () => {
  const newFilterGroups = filterGroups.value.length > 0 ? [...filterGroups.value] : []

  newFilterGroups.push({
    conditions: [{ field: '', operator: '', value: '', logic: 'AND' }],
    groupLogic: 'AND',
  })
  filterGroups.value = newFilterGroups
}

const removeFilterGroup = (index) => {
  if (filterGroups.value.length > 1) {
    const newFilterGroups = [...filterGroups.value]
    newFilterGroups.splice(index, 1)
    filterGroups.value = newFilterGroups
  }
}

// Granularity selector functions
const toggleGranularitySelector = async () => {
  console.log('toggleGranularitySelector called, current state:', showGranularitySelector.value)
  showGranularitySelector.value = !showGranularitySelector.value
  console.log('New dropdown state:', showGranularitySelector.value)

  if (showGranularitySelector.value && availableGranularities.value.length === 0) {
    console.log('Loading granularities...')
    await loadGranularities()
  }
}

const loadGranularities = async () => {
  console.log('loadGranularities called')
  isLoadingGranularities.value = true
  try {
    const granularities = await fetchGranularityKeys()
    console.log('Fetched granularities:', granularities)
    availableGranularities.value = granularities
    console.log('Available granularities updated:', availableGranularities.value)
  } catch (error) {
    console.error('Failed to load granularities:', error)
  } finally {
    isLoadingGranularities.value = false
  }
}

const handleGranularitySelect = (granularity) => {
  console.log('handleGranularitySelect called with:', granularity)

  // Only allow selection of permitted items (handle both boolean and number formats)
  if (!(granularity.is_permitted === true || granularity.is_permitted === 1)) {
    console.log('Granularity not permitted, skipping selection')
    return
  }

  console.log('Updating granularity selection...')
  selectedGranularity.value = granularity.display_name
  selectedGranularityId.value = granularity.id
  selectedGranularityName.value = granularity.name
  showGranularitySelector.value = false

  console.log('Local state updated:', {
    selectedGranularity: selectedGranularity.value,
    selectedGranularityId: selectedGranularityId.value,
    selectedGranularityName: selectedGranularityName.value,
  })

  // Update the component state
  const component = granularityComponent.value
  if (component) {
    const newComponent = {
      ...component,
      granularity: granularity.display_name,
      granularityId: granularity.id,
      granularityName: granularity.name,
    }
    console.log('Updating component state:', newComponent)
    updateComponent(component.componentId, newComponent)
  } else {
    console.error('Granularity component not found in state')
  }
}

const removeIndicator = (indicatorComponent, indexToRemove) => {
  const newIndices = indicatorComponent.indices.filter((_, idx) => idx !== indexToRemove)
  updateComponent(indicatorComponent.componentId, {
    ...indicatorComponent,
    indices: newIndices,
  })
}

const fileInput = ref(null)
function triggerFileInput() {
  fileInput.value && fileInput.value.click()
}
async function handleFileUpload(event) {
  const file = event.target.files[0]
  if (!file) return

  try {
    // Use a composable to handle parsing and adding sheet
    await importAndAddSheet(file)
    // Emit success event for auto-minimize functionality
    emit('file-uploaded', { fileName: file.name, success: true })
    // Optionally, navigate to datasheet page
    emit('goto-datasheet')
  } catch (error) {
    console.error('File upload failed:', error)
    // Emit failure event (optional, for error handling)
    emit('file-uploaded', { fileName: file.name, success: false, error: error.message })
  }
}

// For each filter, track if it should show as expression
function ensureFilterExpressionFlags() {
  filterGroups.value.forEach((group) => {
    group.conditions.forEach((filter) => {
      if (filter._showExpression === undefined) filter._showExpression = false
    })
  })
}
watch(filterGroups, ensureFilterExpressionFlags, { immediate: true, deep: true })

function toggleConditionExpression(groupIdx, filterIdx) {
  filterGroups.value[groupIdx].conditions[filterIdx]._showExpression = true
}
function toggleConditionEdit(groupIdx, filterIdx) {
  filterGroups.value[groupIdx].conditions[filterIdx]._showExpression = false
}

const operatorToChinese = {
  '=': '等于',
  '!=': '不等于',
  '>': '大于',
  '>=': '大于等于',
  '<': '小于',
  '<=': '小于等于',
  LIKE: '包含',
  'NOT LIKE': '不包含',
  IN: '在列表中',
  'NOT IN': '不在列表中',
}

function onOperatorChange(filter) {
  if (filter.operator === 'IN' || filter.operator === 'NOT IN') {
    if (!Array.isArray(filter.value)) {
      filter.value = filter.value !== null && filter.value !== undefined ? [filter.value] : ['']
    }
  } else {
    if (Array.isArray(filter.value)) {
      filter.value = filter.value.join(',')
    }
  }
  handleFilterChange()
}
function addValue(filter) {
  if (!Array.isArray(filter.value)) filter.value = ['']
  filter.value.push('')
  handleFilterChange()
}
function removeValue(filter, idx) {
  if (Array.isArray(filter.value)) {
    filter.value.splice(idx, 1)
    if (filter.value.length === 0) filter.value.push('')
    handleFilterChange()
  }
}

// Dimension selection is now handled by the floating window interface
function removeDimension(dimensionComponent, indexToRemove) {
  const newDimensions = dimensionComponent.dimensions.filter((_, idx) => idx !== indexToRemove)
  updateComponent(dimensionComponent.componentId, {
    ...dimensionComponent,
    dimensions: newDimensions,
  })
}

const showAllDimensions = ref(false)
const showAllIndicators = ref(false)

// --- Activation Logic for Config Order ---
const isGranularityActive = computed(() => true)
const isDateActive = computed(() => !!selectedGranularity.value)
const isIndicatorActive = computed(() => !!selectedGranularity.value)
const isDimensionActive = computed(() => isDateActive.value && hasIndicatorSelected())
const isFilterActive = computed(() => isDimensionActive.value)

function hasIndicatorSelected() {
  // At least one target indicator with at least one index
  return targetIndicators.value.some((ind) => Array.isArray(ind.indices) && ind.indices.length > 0)
}

// Handle add dimension button click
const handleAddDimensionClick = async () => {
  // Fetch dimensions before opening the selector
  await fetchDimensions()
  emit('add-dimension')
}

// Helper functions to handle both old and new data formats
function getIndexKey(index) {
  if (typeof index === 'object' && index.indexId) {
    return index.indexId
  }
  return typeof index === 'string' ? index : index.indexName
}

function getIndexDisplayName(index) {
  if (typeof index === 'object' && index.indexName) {
    return index.indexName
  }
  return typeof index === 'string' ? index : index.indexName
}

function getDimensionKey(dimension) {
  if (typeof dimension === 'object' && dimension.dimensionId) {
    return dimension.dimensionId
  }
  return typeof dimension === 'string' ? dimension : dimension.dimensionName
}

function getDimensionDisplayName(dimension) {
  if (typeof dimension === 'object' && dimension.dimensionName) {
    return dimension.dimensionName
  }
  return typeof dimension === 'string' ? dimension : dimension.dimensionName
}
</script>

<style scoped>
.analysis-object-container {
  display: flex;
  flex-direction: column;
  gap: 1.2rem;
  padding: 0.5rem 0.5rem 0.5rem 0.5rem;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  overflow-y: auto;
  align-items: center;
}
.left-title-bar {
  width: 450px;
  font-size: 1.13rem;
  font-weight: bold;
  color: #222;
  background: #f3f4f6;
  text-align: left;
  padding: 0.7rem 1.6rem 0.5rem 1.6rem;
  border-radius: 10px;
  margin-bottom: 0.2rem;
  letter-spacing: 1px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.table-process-btn {
  background: #fff;
  border: 1px solid #1976d2;
  color: #1976d2;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 500;
  padding: 4px 16px;
  margin-left: 12px;
  cursor: pointer;
  transition:
    background 0.2s,
    color 0.2s;
}
.table-process-btn:hover {
  background: #1976d2;
  color: #fff;
}
.analysis-card {
  background: #fff;
  padding: 1.1rem 1.6rem 1.2rem 1.6rem;
  border-radius: 14px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.06);
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  width: 450px;
}
.card-header {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  margin-bottom: 0.3rem;
}
.card-icon {
  font-size: 1.1rem;
  color: #1976d2;
}
.card-title.main {
  font-size: 1rem;
  font-weight: bold;
  color: #4285f4;
  letter-spacing: 1px;
  flex: 1;
}
.card-add-btn {
  background: none;
  border: none;
  color: #4285f4;
  font-size: 1.1rem;
  cursor: pointer;
  margin-left: auto;
  padding: 0 0.1rem;
  border-radius: 50%;
  transition: background 0.2s;
}
.card-add-btn:hover {
  background: #e3f2fd;
}

/* Date styles */
.date-list {
  display: flex;
  flex-direction: column;
  gap: 0.6rem;
}

.date-row {
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

.date-label {
  color: #444;
  font-weight: bold;
  font-size: 0.95rem;
  min-width: 80px;
  flex-shrink: 0;
}

.date-input {
  border: none;
  border-radius: 5px;
  padding: 0.18rem 0.5rem;
  font-size: 0.95rem;
  color: #222;
  min-width: 220px;
  max-width: 220px;
  margin-left: auto;
}

.indicator-list {
  display: flex;
  flex-direction: column;
  gap: 0.7rem;
}
.indicator-remove-btn {
  background: none;
  border: none;
  color: #ff6b6b;
  font-size: 1.1rem;
  cursor: pointer;
  padding: 0.1rem 0.3rem;
  border-radius: 50%;
  transition: background 0.2s;
  margin-left: 0.2rem;
  flex-shrink: 0;
}
.indicator-remove-btn:hover {
  background: #ffe6e6;
}
.granularity-input {
  border: none;
  background: #f3f6fa;
  border-radius: 5px;
  padding: 0.18rem 0.5rem;
  font-size: 0.95rem;
  color: #222;
  min-width: 220px;
  max-width: 320px;
  appearance: none;
  -webkit-appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='%234285f4' d='M6 8L2 4h8z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 8px center;
  padding-right: 28px;
  cursor: pointer;
}
.granularity-input:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2);
}
/* Filter styles */
.filter-list {
  display: flex;
  flex-direction: column;
  gap: 0.7rem;
}

.filter-item {
  background: #fff;
  border: 1px solid #dbeafe;
  border-radius: 12px;
  padding: 0.5rem 0.6rem;
  display: flex;
  flex-direction: column;
  gap: 0.3rem;
  box-shadow: 0 1px 2px rgba(66, 133, 244, 0.03);
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.filter-label-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.3rem;
  padding-bottom: 0.3rem;
  border-bottom: 1px solid #f0f0f0;
}

.filter-label {
  font-weight: bold;
  font-size: 0.9rem;
  color: #1976d2;
}

.condition-parts {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  width: 100%;
  margin-bottom: 0.3rem;
  flex-wrap: wrap;
}

.field-select {
  border: none;
  background: #f3f6fa;
  border-radius: 5px;
  padding: 0.4rem 0.6rem;
  font-size: 0.85rem;
  color: #222;
  min-width: 80px;
  flex: 1;
  max-width: 120px;
  appearance: none;
  -webkit-appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='%234285f4' d='M6 8L2 4h8z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 6px center;
  padding-right: 24px;
  cursor: pointer;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.field-select:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2);
}

.operator-select {
  border: none;
  background: #f3f6fa;
  border-radius: 5px;
  padding: 0.4rem 0.6rem;
  font-size: 0.85rem;
  color: #222;
  min-width: 60px;
  max-width: 80px;
  flex-shrink: 0;
  appearance: none;
  -webkit-appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='%234285f4' d='M6 8L2 4h8z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 6px center;
  padding-right: 24px;
  cursor: pointer;
}

.operator-select:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2);
}

.value-input {
  border: none;
  background: #f3f6fa;
  border-radius: 5px;
  padding: 0.4rem 0.6rem;
  font-size: 0.85rem;
  color: #222;
  min-width: 60px;
  flex: 1;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.value-input:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2);
}

.filter-remove-btn {
  background: none;
  border: none;
  color: #ff6b6b;
  font-size: 1.1rem;
  cursor: pointer;
  padding: 0.1rem 0.3rem;
  border-radius: 50%;
  transition: background 0.2s;
  margin-left: 0.2rem;
  flex-shrink: 0;
}

.filter-remove-btn:hover {
  background: #ffe6e6;
}

.logic-select {
  border: none;
  background: #f3f6fa;
  border-radius: 5px;
  padding: 0.18rem 0.5rem;
  font-size: 0.95rem;
  color: #222;
  min-width: 80px;
  flex: 1;
  max-width: 100px;
}

/* Filter Group styles */
.filter-group {
  background: #fff;
  border: 1px solid #dbeafe;
  border-radius: 12px;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
  box-shadow: 0 2px 8px rgba(66, 133, 244, 0.08);
  margin-bottom: 1rem;
  position: relative;
}

.filter-group::before {
  content: '';
  position: absolute;
  left: -8px;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 60%;
  background: linear-gradient(135deg, #4285f4, #42b983);
  border-radius: 2px;
}

.group-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #f0f0f0;
}

.group-label {
  font-weight: bold;
  font-size: 1rem;
  color: #1976d2;
}

.group-remove-btn {
  background: none;
  border: none;
  color: #ff6b6b;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.2rem 0.4rem;
  border-radius: 50%;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.group-remove-btn:hover {
  background: #ffe6e6;
  transform: scale(1.1);
}

.group-conditions {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
  padding-left: 0.5rem;
}

.add-condition-btn {
  background: none;
  border: 1px dashed #4285f4;
  color: #4285f4;
  font-size: 0.9rem;
  cursor: pointer;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  align-self: flex-start;
  transition: all 0.2s;
  margin-top: 0.5rem;
}

.add-condition-btn:hover {
  background: #e3f2fd;
  border-color: #1976d2;
  color: #1976d2;
}

.group-logic-select {
  border: 1px solid #dbeafe;
  background: #f8f9fa;
  border-radius: 6px;
  padding: 0.4rem 0.8rem;
  font-size: 0.9rem;
  color: #1976d2;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.group-logic-select:hover {
  border-color: #1976d2;
  background: #e3f2fd;
}

/* Empty filters state */
.empty-filters {
  text-align: center;
  padding: 2rem 1rem;
  color: #888;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px dashed #ddd;
}

.empty-filters p {
  margin: 0.5rem 0;
  font-size: 0.9rem;
}

.empty-hint {
  font-size: 0.8rem !important;
  color: #aaa;
}

@media (max-width: 768px) {
  .condition-parts {
    flex-direction: column;
    gap: 0.5rem;
    align-items: stretch;
  }

  .field-select,
  .operator-select,
  .value-input {
    width: 100%;
    max-width: none;
  }

  .filter-label-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.3rem;
  }

  .filter-label {
    font-size: 0.9rem;
  }
}

.upload-doc-icon {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background 0.2s;
  padding: 1px;
}
.upload-doc-icon:hover {
  background: #e3f2fd;
}

.filter-expression-row {
  display: flex;
  align-items: center;
  background: #f5f8fc;
  border: 1px solid #42b983;
  border-radius: 5px;
  padding: 3px 8px;
  margin-bottom: 4px;
  font-size: 13px;
  font-family: 'JetBrains Mono', 'Menlo', 'Consolas', 'monospace', sans-serif;
  color: #1976d2;
  box-shadow: 0 1px 3px 0 rgba(66, 185, 131, 0.04);
  min-height: 28px;
}
.filter-expression-chip {
  background: #eafaf1;
  color: #1976d2;
  border-radius: 4px;
  padding: 2px 7px;
  font-weight: 600;
  font-size: 13px;
  letter-spacing: 0.2px;
  font-family: 'JetBrains Mono', 'Menlo', 'Consolas', 'monospace', sans-serif;
}
.edit-condition-btn {
  background: none;
  border: none;
  color: #1976d2;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 100%;
  min-width: 24px;
  min-height: 24px;
  border-radius: 4px;
  padding: 0;
  transition: background 0.2s;
  margin-left: auto;
}
.edit-condition-btn svg {
  width: 16px;
  height: 16px;
  display: block;
}
.edit-condition-btn:hover {
  background: #e3f2fd;
}
.to-expression-btn-row {
  margin-top: 4px;
  display: flex;
  justify-content: flex-end;
}
.to-expression-btn {
  background: none;
  border: none;
  color: #42b983;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 2px;
  cursor: pointer;
  border-radius: 4px;
  padding: 2px 6px;
  transition: background 0.2s;
  min-height: 24px;
}
.to-expression-btn:hover {
  background: #eafaf1;
}
.multi-value-inputs {
  display: flex;
  align-items: center;
  gap: 6px;
  flex-wrap: wrap;
}
.multi-input-item {
  display: flex;
  align-items: center;
  background: #f3f6fa;
  border: 1px solid #dbeafe;
  border-radius: 4px;
  padding: 2px 4px;
  margin-right: 2px;
  margin-bottom: 2px;
}
.multi-value-input {
  border: none;
  background: transparent;
  font-size: 13px;
  width: 60px;
  padding: 2px 4px;
  outline: none;
}
.remove-value-btn {
  background: none;
  border: none;
  color: #e53935;
  font-size: 14px;
  margin-left: 2px;
  cursor: pointer;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s;
}
.remove-value-btn:hover {
  background: #ffeaea;
}
.add-value-btn {
  background: none;
  border: 1px dashed #42b983;
  color: #42b983;
  font-size: 16px;
  width: 22px;
  height: 22px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  margin-left: 2px;
  transition:
    background 0.2s,
    border-color 0.2s;
}
.add-value-btn:hover {
  background: #eafaf1;
  border-color: #1976d2;
}
.date-mode-switcher {
  display: flex;
  gap: 0.5rem;
  background: #f3f4f6;
  border-radius: 999px;
  padding: 4px 6px;
  margin-bottom: 0.2rem;
  box-shadow: 0 1px 4px rgba(66, 133, 244, 0.04);
}
.date-mode-btn {
  border: none;
  background: none;
  color: #1976d2;
  font-weight: 500;
  font-size: 0.98rem;
  padding: 6px 22px;
  border-radius: 999px;
  cursor: pointer;
  transition:
    background 0.18s,
    color 0.18s;
}
.date-mode-btn.active {
  background: linear-gradient(90deg, #4285f4 0%, #42b983 100%);
  color: #fff;
  box-shadow: 0 2px 8px rgba(66, 133, 244, 0.1);
}
.date-mode-btn:not(.active):hover {
  background: #e3f2fd;
  color: #1976d2;
}
.indicator-selector-dropdown {
  display: flex;
  align-items: center;
}
.indicator-selector {
  background: #fff;
  border: 1.5px solid #42b983;
  color: #1976d2;
  border-radius: 999px;
  font-size: 1rem;
  font-weight: 500;
  padding: 4px 22px 4px 16px;
  cursor: pointer;
  transition:
    border 0.2s,
    box-shadow 0.2s;
  outline: none;
  min-width: 120px;
  max-width: 135px;
  box-shadow: 0 1px 4px rgba(66, 185, 131, 0.07);
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='%2342b983' d='M6 8L2 4h8z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 12px center;
}
.indicator-selector:focus {
  border: 1.5px solid #1976d2;
  box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.13);
}
/* Button-specific styles for dimension selector */
button.indicator-selector {
  text-align: left;
}
button.indicator-selector:hover {
  background: #f8f9fa;
  border: 1.5px solid #1976d2;
}
button.indicator-selector:disabled {
  background: #f5f5f5;
  border: 1.5px solid #d0d0d0;
  color: #999;
  cursor: not-allowed;
  opacity: 0.6;
}
button.indicator-selector:disabled:hover {
  background: #f5f5f5;
  border: 1.5px solid #d0d0d0;
}
.indicator-tabs {
  display: flex;
  flex-wrap: wrap;
  gap: 4px 0;
}
.indicator-tab {
  display: inline-flex;
  align-items: center;
  background: linear-gradient(90deg, #e3f2fd 0%, #eafaf1 100%);
  color: #1976d2;
  border-radius: 16px;
  padding: 4px 8px 4px 12px;
  font-size: 0.93rem;
  font-weight: 500;
  margin-bottom: 4px;
  box-shadow: 0 1px 4px rgba(66, 133, 244, 0.07);
  border: 1px solid #dbeafe;
  transition:
    background 0.2s,
    color 0.2s;
  max-width: 48%;
  justify-content: flex-start;
  box-sizing: border-box;
  margin-right: 7px;
}
.logic-selector-inline {
  margin-left: 0.5rem;
  display: flex;
  align-items: center;
}
.logic-selector-expression {
  margin-left: 0.5rem;
  display: flex;
  align-items: center;
}
.add-condition-group-row {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 1rem;
  margin-top: 0.5rem;
}
.group-logic-selector-inline {
  margin-left: 1rem;
  display: flex;
  align-items: center;
}
.filter-help-icon {
  font-size: 1.2rem;
  color: #1976d2;
  margin-right: 0.5rem;
  margin-left: 0.5rem;
  cursor: pointer;
  transition: color 0.2s;
  display: flex;
  align-items: center;
}
.filter-help-icon:hover {
  color: #42b983;
}
.filter-help-popover {
  position: absolute;
  top: 148px;
  right: 24px;
  z-index: 100;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 24px rgba(66, 133, 244, 0.13);
  border: 1.5px solid #e3f2fd;
  min-width: 340px;
  max-width: 420px;
  padding: 0;
  animation: fadeIn 0.2s;
}
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
.filter-help-popover-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.2rem 0.5rem 1.2rem;
  border-bottom: 1px solid #e3f2fd;
  font-weight: bold;
  color: #1976d2;
  font-size: 1.08rem;
}
.filter-help-close-btn {
  background: none;
  border: none;
  color: #888;
  font-size: 1.3rem;
  cursor: pointer;
  border-radius: 50%;
  padding: 0 8px;
  transition:
    background 0.2s,
    color 0.2s;
}
.filter-help-close-btn:hover {
  background: #f5f5f5;
  color: #1976d2;
}
.filter-help-popover-content {
  padding: 1rem 1.2rem 1.2rem 1.2rem;
  color: #444;
  font-size: 0.97rem;
}
.filter-help-popover-content ul {
  margin: 0.5rem 0 0.5rem 1.2rem;
  padding-left: 1.2rem;
}
.filter-help-popover-content li {
  margin: 0.3rem 0;
  font-size: 0.95rem;
  color: #555;
  line-height: 1.4;
}
.filter-help-popover-content strong {
  color: #1976d2;
}
.expand-tab {
  background: #fffbe6;
  color: #d48806;
  border: 1px solid #ffe58f;
  cursor: pointer;
  font-weight: 600;
}
.expand-tab:hover {
  background: #fff1b8;
}
.card-inactive {
  opacity: 0.6;
  background: #f5f5f5 !important;
  pointer-events: none;
  filter: grayscale(0.15);
}

/* Granularity Selector Styles */
.granularity-selector-container {
  position: relative;
}

.granularity-selector-btn {
  background: #fff;
  border: 1.5px solid #42b983;
  color: #1976d2;
  border-radius: 999px;
  font-size: 1rem;
  font-weight: 500;
  padding: 4px 22px 4px 16px;
  cursor: pointer;
  transition:
    border 0.2s,
    box-shadow 0.2s;
  outline: none;
  min-width: 120px;
  max-width: 220px;
  box-shadow: 0 1px 4px rgba(66, 185, 131, 0.07);
  display: flex;
  align-items: center;
  justify-content: space-between;
  text-align: left;
}

.granularity-selector-btn:hover {
  background: #f8f9fa;
  border: 1.5px solid #1976d2;
}

.granularity-selector-btn:disabled {
  background: #f5f5f5;
  border: 1.5px solid #d0d0d0;
  color: #999;
  cursor: not-allowed;
  opacity: 0.6;
}

.dropdown-arrow {
  font-size: 0.8rem;
  transition: transform 0.2s;
}

.granularity-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  margin-top: 4px;
  max-height: 400px;
  overflow-y: auto;
  min-width: 300px;
  padding: 12px;
}

/* Simplified granularity dropdown - no sections needed */

.step-label {
  font-size: 0.9rem;
  font-weight: 600;
  color: #1976d2;
  margin-bottom: 8px;
}

.granularity-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
  max-height: 300px;
  overflow-y: auto;
}

.granularity-item {
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  background: #f7faff;
  color: #333;
  transition:
    background 0.2s,
    color 0.2s;
  font-size: 0.9rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.granularity-item:hover {
  background: #e3f2fd;
  color: #1976d2;
}

.granularity-item.active {
  background: #1976d2;
  color: #fff;
}

.granularity-item.disabled {
  background: #f5f5f5;
  color: #999;
  cursor: not-allowed;
  opacity: 0.6;
}

.granularity-item.disabled:hover {
  background: #f5f5f5;
  color: #999;
}

.loading-item {
  padding: 8px 12px;
  color: #666;
  font-size: 0.9rem;
  text-align: center;
}

.empty-item {
  padding: 8px 12px;
  color: #999;
  font-size: 0.9rem;
  text-align: center;
  font-style: italic;
}

.placeholder-item {
  padding: 8px 12px;
  color: #999;
  font-size: 0.9rem;
  text-align: center;
  font-style: italic;
  background: #f9f9f9;
  border-radius: 6px;
}

/* Responsive design for granularity dropdown */
@media (max-width: 768px) {
  .granularity-dropdown {
    min-width: 320px;
    max-height: 300px;
  }

  /* Simplified layout - no sections needed */

  .granularity-list {
    max-height: 120px;
  }
}
</style>
