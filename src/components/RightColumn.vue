<template>
  <div class="calculation-container">
    <div class="title-row">
      <h2 class="title">计算</h2>
      <button class="add-btn" @click="showAddModal = true">+</button>
    </div>
    <div v-if="localComputationStates.length === 0" class="empty-board">
      请点击右上角"+"添加功能模块
    </div>
    <draggable
      v-model="localComputationStates"
      item-key="componentId"
      class="draggable-list"
      :animation="200"
    >
      <template #item="{ element }">
        <div
          class="module-block"
          :class="{
            'wide-module':
              element.componentName === 'trend-result' || element.componentName === 'corr-result',
          }"
        >
          <TrendAnalysisModule v-if="element.componentName === 'trend'" />
          <TrendChartModule v-else-if="element.componentName === 'chart'" />
          <TrendAnalysisResult
            v-else-if="element.componentName === 'trend-result'"
            :result="element.result"
          />
          <CorrAnalysisModule v-else-if="element.componentName === 'corr'" />
          <CorrAnalysisResult
            v-else-if="element.componentName === 'corr-result'"
            :result="element.result"
          />
        </div>
      </template>
    </draggable>
    <!-- 添加模块弹窗 -->
    <div v-if="showAddModal" class="modal-mask">
      <div class="modal-content">
        <h3 style="color: #000">选择要添加的模块</h3>
        <button class="modal-btn" @click="addModule('trend')">趋势分析模块</button>
        <!-- <button class="modal-btn" @click="addModule('chart')">趋势分析结果图模块</button> -->
        <button class="modal-btn" @click="addModule('corr')">相关性分析模块</button>
        <button class="modal-btn cancel" @click="showAddModal = false">取消</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { storeToRefs } from 'pinia'
import { ref, watch } from 'vue'
import draggable from 'vuedraggable'
import { v4 as uuidv4 } from 'uuid'
import TrendAnalysisModule from './TrendAnalysisModule.vue'
import TrendChartModule from './TrendChartModule.vue'
import TrendAnalysisResult from './TrendAnalysisResult.vue'
import CorrAnalysisModule from './CorrAnalysisModule.vue'
import CorrAnalysisResult from './CorrAnalysisResult.vue'
import { useStates } from '../store/states'

const statesStore = useStates()
const { updateComponent } = statesStore
const { states } = storeToRefs(statesStore)
const showAddModal = ref(false)

// 当前计算模块状态
const localComputationStates = ref([])

// 添加趋势分析结果模块的函数
const addTrendResultModule = (result) => {
  const componentId = uuidv4()
  const newComponent = {
    componentId,
    componentName: 'trend-result',
    result: result,
  }
  // 先添加到本地状态
  localComputationStates.value = [...localComputationStates.value, newComponent]
  // 然后更新store
  updateComponent(componentId, newComponent)
}

// 添加相关性分析结果模块的函数
const addCorrResultModule = (result) => {
  const componentId = uuidv4()
  const newComponent = {
    componentId,
    componentName: 'corr-result',
    result: result,
  }
  // 先添加到本地状态
  localComputationStates.value = [...localComputationStates.value, newComponent]
  // 然后更新store
  updateComponent(componentId, newComponent)
}

// 暴露给父组件使用
defineExpose({
  addTrendResultModule,
  addCorrResultModule,
})

// 监听store中的变化并更新本地状态
watch(
  () => states.value,
  (newStates) => {
    // 过滤计算模块并更新本地状态
    const computationStates = newStates.filter((state) => {
      // 检查是否是计算相关的模块
      const isComputationModule =
        state.componentName === 'trend' ||
        state.componentName === 'chart' ||
        state.componentName === 'trend-result' ||
        state.componentName === 'corr' ||
        state.componentName === 'corr-result'

      // 如果是结果模块，确保它有result数据
      if (state.componentName === 'trend-result' || state.componentName === 'corr-result') {
        return isComputationModule && state.result
      }

      return isComputationModule
    })

    // 更新本地状态
    localComputationStates.value = computationStates
  },
  { immediate: true, deep: true },
)

// 监听本地状态的变化并更新store
watch(
  localComputationStates,
  (newOrder) => {
    // 从store中获取非计算模块
    const nonComputationStates = states.value.filter(
      (state) =>
        state.componentName !== 'trend' &&
        state.componentName !== 'chart' &&
        state.componentName !== 'trend-result' &&
        state.componentName !== 'corr' &&
        state.componentName !== 'corr-result',
    )
    // 更新store中的每个计算模块
    newOrder.forEach((component) => {
      updateComponent(component.componentId, component)
    })
  },
  { deep: true },
)

function addModule(type) {
  const componentId = uuidv4()
  const newComponent = { componentId, componentName: type }
  // 先添加到本地状态
  localComputationStates.value = [...localComputationStates.value, newComponent]
  // 然后更新store
  updateComponent(componentId, newComponent)
  showAddModal.value = false
}
</script>

<style scoped>
.calculation-container {
  padding: 1rem;
  min-height: 400px;
  background: #f7faff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  overflow-y: auto;
}
.title-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}
.title {
  margin: 0;
  text-align: center;
  flex-grow: 1;
  color: #000;
  font-size: 1.2rem;
  font-weight: bold;
}
.add-btn {
  background: #42b983;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  font-size: 1.2rem;
}
.add-btn:hover {
  opacity: 0.9;
}
.empty-board {
  color: #bbb;
  text-align: center;
  margin-top: 3rem;
  font-size: 1.1rem;
}
.module-block {
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  margin-bottom: 0;
  padding: 1.5rem 1rem;
  width: 200px;
  min-width: 200px;
  max-width: 220px;
  min-height: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-left: 0;
  margin-right: 0;
}
/* 模块样式 */
.trend-module {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
  width: 100%;
}
.trend-title {
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  justify-content: center;
}
.trend-arrow {
  color: #8bc34a;
  font-size: 1.5rem;
  font-weight: bold;
}
.trend-btns {
  display: flex;
  flex-direction: column;
  gap: 0.7rem;
  width: 100%;
}
.trend-btn {
  background: #fff;
  border: 1px solid #bbb;
  border-radius: 20px;
  padding: 0.5rem 1.2rem;
  font-size: 1rem;
  cursor: pointer;
  transition: box-shadow 0.2s;
  width: 100%;
}
.trend-btn:hover {
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.08);
}
.chart-module {
  width: 100%;
  min-height: 120px;
  background: #f9f9fb;
  border-radius: 10px;
  padding: 0.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.chart-title {
  font-weight: bold;
  color: #1976d2;
  margin-bottom: 0.5rem;
  text-align: center;
}
/* 弹窗样式 */
.modal-mask {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}
.modal-content {
  background: #fff;
  border-radius: 10px;
  padding: 2rem 2.5rem 1.5rem 2.5rem;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.13);
  min-width: 260px;
  text-align: center;
}
.modal-btn {
  display: block;
  width: 100%;
  margin: 0.7rem 0;
  padding: 0.6rem 0;
  border: none;
  border-radius: 6px;
  background: #f5f5f5;
  color: #1976d2;
  font-size: 1rem;
  cursor: pointer;
  transition: background 0.2s;
}
.modal-btn:hover {
  background: #e3f2fd;
}
.modal-btn.cancel {
  color: #888;
  background: #f8f8f8;
}
.draggable-list {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 2rem;
  background: #f3f4f6;
  border-radius: 12px;
  padding: 2rem 0.5rem;
  min-height: 300px;
  justify-content: flex-start;
  align-items: flex-start;
}
.wide-module {
  width: 400px;
  min-width: 400px;
  max-width: 600px;
}
@media (max-width: 768px) {
  .module-block {
    width: 100%;
    min-width: 100%;
    max-width: 100%;
  }

  .wide-module {
    width: 100%;
    min-width: 100%;
    max-width: 100%;
  }

  .draggable-list {
    padding: 1rem;
  }
}
</style>
