<template>
  <div class="chat-history">
    <div class="history-header">
      <h2>聊天历史</h2>
      <div class="history-actions">
        <button @click="startNewChat" class="action-button">新对话</button>
        <button @click="saveCurrentChat" class="action-button">保存会话</button>
      </div>
    </div>
    <div class="history-list">
      <div
        v-for="session in sessions"
        :key="session.id"
        class="session-item"
        @click="loadSession(session.id)"
      >
        <div class="session-title">{{ session.title }}</div>
        <div class="session-time">{{ session.timestamp }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  name: 'ChatHistory',
  data() {
    return {
      sessions: [],
    }
  },
  methods: {
    startNewChat() {
      // 触发父组件的事件，清空消息
      this.$emit('new-chat')
    },
    generateDefaultTitle(messages) {
      if (!messages || messages.length === 0) {
        return `会话 ${new Date().toLocaleString()}`
      }

      // 获取第一条用户消息
      const firstUserMessage = messages.find((msg) => msg.type === 'user')
      if (!firstUserMessage) {
        return `会话 ${new Date().toLocaleString()}`
      }

      // 清理消息内容
      let title = firstUserMessage.content
        .replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g, '') // 只保留中文、英文和数字
        .trim()

      // 限制长度为10个字符
      if (title.length > 10) {
        title = title.substring(0, 10)
      }

      return title || `会话 ${new Date().toLocaleString()}`
    },
    async saveCurrentChat() {
      try {
        const messages = this.$parent.$refs.chatMain.messages
        const defaultTitle = this.generateDefaultTitle(messages)

        await axios.post('/api/save-session', {
          messages: messages,
          title: defaultTitle,
        })

        // 刷新会话列表
        this.loadSessions()

        // 显示成功提示
        alert('会话保存成功！')
      } catch (error) {
        console.error('保存会话失败:', error)
        alert('保存会话失败，请重试')
      }
    },
    async loadSessions() {
      try {
        const response = await axios.get('/api/get-sessions')
        this.sessions = response.data
      } catch (error) {
        console.error('加载会话列表失败:', error)
      }
    },
    async loadSession(sessionId) {
      try {
        const response = await axios.get(`/api/get-session/${sessionId}`)
        // 触发父组件的事件，加载会话
        this.$emit('load-session', response.data.messages)
      } catch (error) {
        console.error('加载会话失败:', error)
        alert('加载会话失败，请重试')
      }
    },
  },
  mounted() {
    this.loadSessions()
  },
}
</script>

<style scoped>
.chat-history {
  width: 250px;
  border-right: 1px solid #e0e0e0;
  padding: 20px;
  background-color: #f5f5f5;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.history-header {
  margin-bottom: 20px;
}

.history-header h2 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #333;
}

.history-actions {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.action-button {
  flex: 1;
  padding: 8px;
  background-color: #4caf50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.action-button:hover {
  background-color: #45a049;
}

.history-list {
  flex: 1;
  overflow-y: auto;
}

.session-item {
  padding: 10px;
  border-bottom: 1px solid #e0e0e0;
  cursor: pointer;
  transition: background-color 0.2s;
}

.session-item:hover {
  background-color: #e8e8e8;
}

.session-title {
  font-weight: 500;
  margin-bottom: 5px;
}

.session-time {
  font-size: 12px;
  color: #666;
}
</style>
