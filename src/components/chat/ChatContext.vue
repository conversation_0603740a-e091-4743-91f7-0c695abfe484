<template>
  <div class="chat-context">
    <h2>上下文信息</h2>
    <div class="context-content">
      <!-- 这里将显示聊天上下文信息 -->
    </div>
  </div>
</template>

<script>
export default {
  name: 'ChatContext',
}
</script>

<style scoped>
.chat-context {
  width: 300px;
  border-left: 1px solid #e0e0e0;
  padding: 20px;
  background-color: #f5f5f5;
  height: 100%;
}

h2 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #333;
}

.context-content {
  height: calc(100% - 60px);
  overflow-y: auto;
}
</style>
