<template>
  <div class="chatmain-outer">
    <div class="chatmain-root">
      <div class="chatmain-messages" ref="messagesContainer">
        <div
          v-for="(message, index) in messages"
          :key="index"
          class="chatmain-message-row"
          :class="message.type"
        >
          <div class="chatmain-avatar">
            <span v-if="message.type === 'user'">
              <!-- User SVG Icon -->
              <svg width="44" height="44" viewBox="0 0 44 44" fill="none">
                <circle cx="22" cy="22" r="22" fill="#eaf6ff" />
                <circle cx="22" cy="18" r="8" fill="#4A90E2" />
                <ellipse cx="22" cy="32" rx="12" ry="7" fill="#b3d6f6" />
              </svg>
            </span>
            <span v-else>
              <!-- Robot SVG Icon -->
              <svg width="44" height="44" viewBox="0 0 44 44" fill="none">
                <circle cx="22" cy="22" r="22" fill="#f7faff" />
                <rect x="11" y="16" width="22" height="14" rx="7" fill="#4A90E2" />
                <rect x="16" y="13" width="12" height="6" rx="3" fill="#b3d6f6" />
                <circle cx="17.5" cy="23" r="2" fill="#fff" />
                <circle cx="26.5" cy="23" r="2" fill="#fff" />
                <rect x="20" y="27" width="4" height="2" rx="1" fill="#fff" />
              </svg>
            </span>
          </div>
          <div class="chatmain-bubble">
            <div class="chatmain-meta" v-if="message.analysisObject || message.fields">
              <span v-if="message.analysisObject" class="chatmain-tag chatmain-tag-blue">
                {{ message.analysisObject }}
              </span>
              <span v-for="field in message.fields || []" :key="field" class="chatmain-tag">
                {{ field }}
              </span>
            </div>
            <!-- Per-message think toggle for server messages with <think> -->
            <template v-if="message.type === 'server' && extractThink(message.content).think">
              <div class="chatmain-think-toggle">
                <button @click="toggleThink(index)" class="think-btn">
                  {{ showThink[index] ? '隐藏思考过程' : '查看思考过程' }}
                </button>
              </div>
            </template>
            <!-- Think section, only if toggled, and above main content -->
            <div
              v-if="showThink[index] && extractThink(message.content).think"
              class="chatmain-think-section"
            >
              <p v-html="formatContent(extractThink(message.content).think)"></p>
            </div>
            <!-- Main content (excluding <think>) -->
            <div
              class="chatmain-content"
              v-html="formatContent(extractThink(message.content).main)"
            ></div>
          </div>
        </div>
      </div>
      <!-- Loading indicator -->
      <div v-if="loading" class="chatmain-loading">正在等待回复...</div>
      <div class="chatmain-bottom">
        <div class="chatmain-divider"></div>
        <div class="chatmain-knowledge-row">
          <span class="chatmain-knowledge-label">选择知识库</span>
          <span class="chatmain-knowledge-icon">
            <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
              <rect
                x="3"
                y="5"
                width="18"
                height="14"
                rx="2.5"
                stroke="#4A90E2"
                stroke-width="1.5"
              />
              <rect x="7" y="9" width="10" height="2" rx="1" fill="#4A90E2" />
              <rect x="7" y="13" width="6" height="2" rx="1" fill="#4A90E2" />
            </svg>
          </span>
          <select class="chatmain-knowledge-select">
            <option>个贷催收场景分析</option>
            <option>知识库2</option>
          </select>
        </div>
        <div class="chatmain-inputbar">
          <label class="chatmain-upload">
            <input type="file" style="display: none" @change="handleFileUpload" />
            <svg width="22" height="22" fill="none" viewBox="0 0 24 24">
              <path
                d="M16.5 9.4V7A4.5 4.5 0 0 0 7.5 7v2.4"
                stroke="#4A90E2"
                stroke-width="1.5"
                stroke-linecap="round"
              />
              <rect
                x="3"
                y="9.4"
                width="18"
                height="10.6"
                rx="2.5"
                stroke="#4A90E2"
                stroke-width="1.5"
              />
              <path d="M12 14.5v2.5" stroke="#4A90E2" stroke-width="1.5" stroke-linecap="round" />
            </svg>
          </label>
          <input
            type="text"
            v-model="inputMessage"
            placeholder="请直接向我提问，或者从上方选择知识库进行提问"
            @keyup.enter="handleClickSend"
          />
          <button class="chatmain-send" @click="handleClickSend">
            <svg width="24" height="24" fill="none" viewBox="0 0 24 24">
              <path
                d="M3 12L21 3L14.25 21L11.25 13.5L3 12Z"
                stroke="#4A90E2"
                stroke-width="1.5"
                stroke-linejoin="round"
              />
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'
import { useChatFlow } from '@/composables/useChatFlow'

export default {
  name: 'ChatMain',
  emits: ['analysis-result'],
  setup(_, { emit }) {
    const { messages, inputMessage, sendMessage, loading } = useChatFlow()
    // Per-message think toggle state
    const showThink = reactive({})
    function toggleThink(idx) {
      showThink[idx] = !showThink[idx]
    }
    // Extract <think>...</think> and main content
    function extractThink(content) {
      if (!content) return { main: '', think: '' }
      const thinkMatch = content.match(/<think>([\s\S]*?)<\/think>/)
      if (thinkMatch) {
        return {
          main: content.replace(/<think>[\s\S]*?<\/think>/, '').trim(),
          think: thinkMatch[1].trim(),
        }
      } else {
        return { main: content, think: '' }
      }
    }
    // 格式化内容，支持加粗/高亮/换行
    function formatContent(content) {
      if (!content) return ''
      // 粗体：**text**
      let html = content.replace(/\*\*(.+?)\*\*/g, '<b>$1</b>')
      // 高亮数字（亿/万/元/百分号）
      html = html.replace(
        /(\d+[.,]?\d*\s*(亿|万|元|%|\d+\.\d+))/g,
        '<span class="chatmain-highlight">$1</span>',
      )
      // 换行
      html = html.replace(/\n/g, '<br>')
      return html
    }
    async function handleClickSend() {
      const resp = await sendMessage()
      if (resp && resp.analysisResult) {
        emit('analysis-result', resp.analysisResult)
      }
      setTimeout(() => {
        const container = document.querySelector('.chatmain-messages')
        if (container) container.scrollTop = container.scrollHeight
      }, 100)
    }
    function handleFileUpload(e) {
      // 这里可以处理文件上传逻辑
      // 目前仅做占位
      alert('文件上传功能待实现')
    }
    return {
      messages,
      inputMessage,
      sendMessage,
      loading,
      showThink,
      toggleThink,
      extractThink,
      handleClickSend,
      handleFileUpload,
      formatContent,
    }
  },
}
</script>

<style scoped>
.chatmain-outer {
  width: 100%;
  height: 100%;
  background: #f5f6fa;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-end;
  padding-left: 32px;
  box-sizing: border-box;
}

.chatmain-root {
  width: 100%;
  max-width: 900px;
  min-width: 420px;
  height: 90vh;
  min-height: 420px;
  background: #fff;
  border-radius: 18px;
  box-shadow: 0 4px 24px rgba(80, 120, 200, 0.08);
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  padding: 0;
}

.chatmain-messages {
  flex: 1;
  overflow-y: auto;
  padding: 32px 32px 16px 32px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.chatmain-message-row {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.chatmain-message-row.user {
  flex-direction: row-reverse;
}

.chatmain-avatar img,
.chatmain-avatar svg {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  background: #f0f0f0;
  border: 1.5px solid #e0e0e0;
  object-fit: cover;
  display: block;
}

.chatmain-bubble {
  max-width: 75%;
  background: #f7faff;
  border-radius: 18px;
  box-shadow: 0 2px 12px rgba(80, 120, 200, 0.06);
  padding: 18px 22px 16px 22px;
  font-size: 1.08rem;
  line-height: 1.8;
  color: #222;
  word-break: break-word;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.chatmain-message-row.user .chatmain-bubble {
  background: #eaf6ff;
  color: #222;
  border-bottom-right-radius: 6px;
  border-bottom-left-radius: 18px;
}

.chatmain-message-row.server .chatmain-bubble {
  background: #fff;
  color: #222;
  border-bottom-left-radius: 6px;
  border-bottom-right-radius: 18px;
}

.chatmain-meta {
  margin-bottom: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.chatmain-tag {
  background: #f0f4fa;
  color: #4a90e2;
  border-radius: 6px;
  padding: 2px 10px;
  font-size: 0.95em;
  font-weight: 500;
}
.chatmain-tag-blue {
  background: #e6f0fa;
  color: #2176d2;
}

.chatmain-content {
  width: 100%;
}

.chatmain-highlight {
  color: #2176d2;
  font-weight: bold;
}

.chatmain-bottom {
  width: 100%;
  background: #f3f6fb;
  display: flex;
  flex-direction: column;
  gap: 0;
  padding: 0 32px 32px 32px;
  box-sizing: border-box;
  border-bottom-left-radius: 18px;
  border-bottom-right-radius: 18px;
}

.chatmain-knowledge-row {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 18px;
  font-size: 1.08rem;
  font-weight: 500;
  color: #888;
}
.chatmain-knowledge-label {
  color: #888;
  font-weight: 600;
  margin-right: 2px;
}
.chatmain-knowledge-icon {
  display: flex;
  align-items: center;
  margin-right: 4px;
}
.chatmain-knowledge-select {
  border: none;
  background: #f7faff;
  color: #2176d2;
  font-size: 1.08rem;
  font-weight: 600;
  cursor: pointer;
  outline: none;
  border-radius: 16px;
  padding: 4px 18px 4px 12px;
  box-shadow: 0 1px 4px rgba(80, 120, 200, 0.06);
  border: 1.5px solid #e0e6f0;
  transition:
    border 0.2s,
    background 0.2s;
}
.chatmain-knowledge-select:hover {
  border: 1.5px solid #4a90e2;
  background: #eaf6ff;
}

.chatmain-divider {
  width: 100%;
  height: 1px;
  background: linear-gradient(to right, #e0e6f0 60%, #f3f6fb 100%);
  margin-bottom: 18px;
  border: none;
}

.chatmain-inputbar {
  display: flex;
  align-items: center;
  gap: 14px;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(80, 120, 200, 0.1);
  padding: 10px 18px 10px 18px;
  border: none;
  margin-top: 0;
  margin-bottom: 0;
}

.chatmain-upload {
  cursor: pointer;
  display: flex;
  align-items: center;
  margin-right: 6px;
  border-radius: 50%;
  transition: background 0.2s;
  padding: 6px;
}
.chatmain-upload:hover {
  background: #eaf6ff;
}

.chatmain-inputbar input[type='text'] {
  flex: 1;
  padding: 12px 16px;
  border: none;
  border-radius: 8px;
  font-size: 1.08rem;
  background: transparent;
  color: #888;
  transition: color 0.2s;
}

.chatmain-inputbar input[type='text']:focus {
  outline: none;
  color: #222;
}

.chatmain-send {
  background: #4a90e2;
  border: none;
  border-radius: 50%;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition:
    background 0.2s,
    box-shadow 0.2s;
  box-shadow: 0 2px 8px rgba(80, 120, 200, 0.1);
}
.chatmain-send svg {
  fill: #fff;
}
.chatmain-send:hover {
  background: #2176d2;
  box-shadow: 0 4px 16px rgba(80, 120, 200, 0.13);
}

.chatmain-loading {
  text-align: center;
  color: #2176d2;
  font-size: 1.1em;
  margin: 10px 0;
}
.chatmain-think-toggle {
  text-align: right;
  margin: 8px 32px 0 0;
}
.think-btn {
  background: #eaf6ff;
  color: #2176d2;
  border: none;
  border-radius: 8px;
  padding: 4px 14px;
  cursor: pointer;
  font-size: 1em;
  transition: background 0.2s;
}
.think-btn:hover {
  background: #d0eaff;
}
.chatmain-think-section {
  background: #f7faff;
  border-radius: 10px;
  margin: 0 32px 10px 32px;
  padding: 14px 18px;
  color: #444;
  font-size: 1em;
}
</style>
