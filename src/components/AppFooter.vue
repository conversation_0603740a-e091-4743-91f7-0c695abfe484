<template>
  <footer class="footer">
    <div class="footer-content">
      <p class="footer-text">本项目仅供个人学习交流使用，请勿将本项目及相关技术用于其他用途。</p>
    </div>
  </footer>
</template>

<script setup>
// Footer component for global use
</script>

<style scoped>
.footer {
  width: 100%;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
  padding: 16px 0;
  margin-top: auto;
  position: relative;
  z-index: 100;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 32px;
  text-align: center;
}

.footer-text {
  margin: 0;
  font-size: 14px;
  color: #6c757d;
  line-height: 1.5;
  font-weight: 400;
}

@media (max-width: 768px) {
  .footer-content {
    padding: 0 16px;
  }

  .footer-text {
    font-size: 12px;
  }
}
</style>
