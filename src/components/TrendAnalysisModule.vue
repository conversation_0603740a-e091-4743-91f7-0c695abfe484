<template>
  <div class="trend-module">
    <div class="trend-title">
      <span style="color: #1976d2; font-weight: bold">📈 趋势分析</span>
      <button
        class="trend-arrow"
        :disabled="selectedTrends.length === 0"
        @click="handleAnalysis"
        :class="{ analyzing: isAnalyzing, error: error }"
      >
        »
      </button>
    </div>
    <div class="trend-selector">
      <CustomSelect v-model="selectedTrends" :groups="selectGroups" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import CustomSelect from './common/CustomSelect.vue'
import { useTrendAnalysis } from '../composables/useTrendAnalysis'
import { useStates } from '../store/states'
import { v4 as uuidv4 } from 'uuid'

const { availableIndicators, availableDimensions, selectedTrends, runAnalysis } = useTrendAnalysis()
const statesStore = useStates()
const { updateComponent } = statesStore

const isAnalyzing = ref(false)
const error = ref(false)

// Format data for CustomSelect component
const selectGroups = computed(() => [
  {
    label: '目标指标',
    options: availableIndicators.value.map((indicator) => ({
      value: indicator,
      label: indicator,
    })),
  },
  {
    label: '目标维度',
    options: availableDimensions.value.map((dimension) => ({
      value: dimension,
      label: dimension,
    })),
  },
])

// Watch for changes in available options to filter out invalid selections
watch(
  availableIndicators,
  (newIndicators) => {
    const validTrends = selectedTrends.value.filter(
      (trend) => newIndicators.includes(trend) || availableDimensions.value.includes(trend),
    )

    if (validTrends.length !== selectedTrends.value.length) {
      selectedTrends.value = validTrends
    }
  },
  { deep: true },
)

watch(
  availableDimensions,
  (newDimensions) => {
    const validTrends = selectedTrends.value.filter(
      (trend) => availableIndicators.value.includes(trend) || newDimensions.includes(trend),
    )

    if (validTrends.length !== selectedTrends.value.length) {
      selectedTrends.value = validTrends
    }
  },
  { deep: true },
)

const handleAnalysis = async () => {
  if (selectedTrends.value.length === 0 || isAnalyzing.value) return

  try {
    isAnalyzing.value = true
    error.value = false
    const result = await runAnalysis()

    // Create a new result module
    const componentId = uuidv4()
    const newComponent = {
      componentId,
      componentName: 'trend-result',
      result: result,
    }
    // Update store with the new result module
    updateComponent(componentId, newComponent)
  } catch (err) {
    console.error('Analysis failed:', err)
    error.value = true
  } finally {
    isAnalyzing.value = false
  }
}
</script>

<style scoped>
.trend-module {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
  width: 100%;
}

.trend-title {
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  justify-content: center;
}

.trend-arrow {
  color: #8bc34a;
  font-size: 1.5rem;
  font-weight: bold;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.trend-arrow:hover:not(:disabled) {
  background: #e8f5e9;
  transform: scale(1.1);
}

.trend-arrow:disabled {
  color: #ccc;
  cursor: not-allowed;
}

.trend-arrow.analyzing {
  animation: spin 1s linear infinite;
  color: #1976d2;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.trend-arrow.error {
  color: #f44336;
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%,
  100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5px);
  }
  75% {
    transform: translateX(5px);
  }
}

.trend-selector {
  width: 100%;
  display: flex;
  justify-content: center;
}
</style>
