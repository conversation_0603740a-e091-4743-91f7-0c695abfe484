<template>
  <div class="task-history-panel" :class="{ 'panel-open': isOpen }">
    <div class="history-header">
      <h3 class="history-title">历史任务</h3>
      <button @click="$emit('close')" class="close-btn">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
          <path
            d="M18 6L6 18M6 6l12 12"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </button>
    </div>

    <div class="history-content">
      <div class="history-filters">
        <input v-model="searchQuery" placeholder="搜索任务..." class="search-input" />
        <select v-model="statusFilter" class="status-filter">
          <option value="">全部状态</option>
          <option value="completed">已完成</option>
          <option value="running">运行中</option>
          <option value="failed">失败</option>
        </select>
      </div>

      <div class="task-list">
        <div v-for="task in filteredTasks" :key="task.id" class="task-item" :class="task.status">
          <div class="task-header">
            <div class="task-title">{{ task.title }}</div>
            <div class="task-status" :class="task.status">
              {{ getStatusText(task.status) }}
            </div>
          </div>
          <div class="task-details">
            <div class="task-time">{{ formatTime(task.createdAt) }}</div>
            <div class="task-duration" v-if="task.duration">
              耗时: {{ formatDuration(task.duration) }}
            </div>
          </div>
          <div class="task-actions">
            <button @click="viewTask(task)" class="action-btn view-btn">查看</button>
            <button
              @click="rerunTask(task)"
              class="action-btn rerun-btn"
              v-if="task.status === 'failed'"
            >
              重试
            </button>
          </div>
        </div>

        <div v-if="filteredTasks.length === 0" class="empty-state">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" class="empty-icon">
            <path
              d="M12 8v4l3 3"
              stroke="#ccc"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <circle cx="12" cy="12" r="10" stroke="#ccc" stroke-width="2" />
          </svg>
          <p>暂无历史任务</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const emit = defineEmits(['close', 'view-task', 'rerun-task'])

const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false,
  },
})

const searchQuery = ref('')
const statusFilter = ref('')

// Mock data - replace with real data from your backend
const tasks = ref([
  {
    id: 1,
    title: '个贷催收场景分析',
    status: 'completed',
    createdAt: new Date('2024-01-15T10:30:00'),
    duration: 1800000, // 30 minutes in ms
    description: '分析个贷催收场景的经营数据',
  },
  {
    id: 2,
    title: '信用卡逾期归因分析',
    status: 'running',
    createdAt: new Date('2024-01-15T14:20:00'),
    duration: null,
    description: '信用卡逾期用户的归因分析',
  },
  {
    id: 3,
    title: '小微企业风险评估',
    status: 'failed',
    createdAt: new Date('2024-01-14T16:45:00'),
    duration: 900000, // 15 minutes in ms
    description: '小微企业信用风险评估模型',
  },
  {
    id: 4,
    title: '消费金融场景分析',
    status: 'completed',
    createdAt: new Date('2024-01-14T09:15:00'),
    duration: 2400000, // 40 minutes in ms
    description: '消费金融场景的数据分析',
  },
])

const filteredTasks = computed(() => {
  return tasks.value.filter((task) => {
    const matchesSearch =
      task.title.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      task.description.toLowerCase().includes(searchQuery.value.toLowerCase())
    const matchesStatus = !statusFilter.value || task.status === statusFilter.value
    return matchesSearch && matchesStatus
  })
})

const getStatusText = (status) => {
  const statusMap = {
    completed: '已完成',
    running: '运行中',
    failed: '失败',
  }
  return statusMap[status] || status
}

const formatTime = (date) => {
  return new Intl.DateTimeFormat('zh-CN', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(date)
}

const formatDuration = (duration) => {
  const minutes = Math.floor(duration / 60000)
  const seconds = Math.floor((duration % 60000) / 1000)
  return `${minutes}分${seconds}秒`
}

const viewTask = (task) => {
  emit('view-task', task)
}

const rerunTask = (task) => {
  emit('rerun-task', task)
}
</script>

<style scoped>
.task-history-panel {
  position: fixed;
  top: 64px;
  left: -320px;
  width: 320px;
  height: calc(100vh - 64px);
  background: #fff;
  border-right: 1px solid #e0e0e0;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  transition: left 0.3s ease;
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.task-history-panel.panel-open {
  left: 0;
}

.history-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #e0e0e0;
  background: #f8f9fa;
}

.history-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  color: #666;
  transition: all 0.2s;
}

.close-btn:hover {
  background: #e9ecef;
  color: #333;
}

.history-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.history-filters {
  padding: 16px 20px;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.search-input {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s;
}

.search-input:focus {
  border-color: #42b983;
}

.status-filter {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  outline: none;
  background: #fff;
  cursor: pointer;
}

.task-list {
  flex: 1;
  overflow-y: auto;
  padding: 16px 20px;
}

.task-item {
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  transition: all 0.2s;
  cursor: pointer;
}

.task-item:hover {
  border-color: #42b983;
  box-shadow: 0 2px 8px rgba(66, 185, 131, 0.1);
}

.task-item.completed {
  border-left: 4px solid #42b983;
}

.task-item.running {
  border-left: 4px solid #1976d2;
}

.task-item.failed {
  border-left: 4px solid #e53935;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.task-title {
  font-weight: 600;
  color: #333;
  font-size: 14px;
  line-height: 1.4;
  flex: 1;
  margin-right: 8px;
}

.task-status {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.task-status.completed {
  background: #e8f5e8;
  color: #2e7d32;
}

.task-status.running {
  background: #e3f2fd;
  color: #1565c0;
}

.task-status.failed {
  background: #ffebee;
  color: #c62828;
}

.task-details {
  margin-bottom: 12px;
}

.task-time {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.task-duration {
  font-size: 12px;
  color: #888;
}

.task-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  padding: 4px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: #fff;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.view-btn {
  color: #1976d2;
  border-color: #1976d2;
}

.view-btn:hover {
  background: #1976d2;
  color: #fff;
}

.rerun-btn {
  color: #e53935;
  border-color: #e53935;
}

.rerun-btn:hover {
  background: #e53935;
  color: #fff;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #999;
  text-align: center;
}

.empty-icon {
  margin-bottom: 16px;
}

.empty-state p {
  margin: 0;
  font-size: 14px;
}

@media (max-width: 768px) {
  .task-history-panel {
    width: 280px;
    left: -280px;
  }
}
</style>
