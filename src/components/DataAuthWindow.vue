<template>
  <div class="data-auth-mask">
    <div class="data-auth-window">
      <div class="data-auth-header">
        <h2>数据权限管理</h2>
        <button class="data-auth-close" @click="$emit('close')">×</button>
      </div>
      <div class="data-auth-flex">
        <div class="data-auth-col">
          <div class="data-auth-label">选择部门</div>
          <CustomSelect v-model="selectedDepts" :groups="deptSelectGroups" :maxDisplay="2" />
        </div>
        <div class="data-auth-col">
          <div class="data-auth-label">分配数据表</div>
          <CustomSelect v-model="selectedTables" :groups="tableSelectGroups" :maxDisplay="3" />
        </div>
      </div>
      <div class="data-auth-btns">
        <button class="data-auth-save-btn" @click="handleAddOrEdit">
          {{ isEditing ? '保存修改' : '添加分配' }}
        </button>
        <button v-if="isEditing" class="data-auth-cancel-btn" @click="resetEdit">取消</button>
      </div>
      <div class="data-auth-table-section">
        <div class="data-auth-table-title">当前部门-数据表权限分配</div>
        <div class="data-auth-table-card">
          <table class="data-auth-table">
            <thead>
              <tr>
                <th>部门</th>
                <th>数据表</th>
                <th style="width: 120px">操作</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(row, idx) in deptTableMap" :key="row.deptId">
                <td>{{ deptName(row.deptId) }}</td>
                <td>{{ tableNames(row.tableIds).join(', ') }}</td>
                <td class="data-auth-btn-cell">
                  <button class="data-auth-edit-btn" @click="handleEdit(row, idx)">
                    <span class="icon-edit" aria-label="编辑">✏️</span> 编辑
                  </button>
                  <button class="data-auth-del-btn" @click="handleDelete(idx)">
                    <span class="icon-del" aria-label="删除">🗑️</span> 删除
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import CustomSelect from './common/CustomSelect.vue'

// Mock 部门和数据表
const departments = ref([
  { id: 'd1', name: '总行大数据中心' },
  { id: 'd2', name: '分行A' },
  { id: 'd3', name: '分行B' },
  { id: 'd4', name: '分行C' },
])
const tables = ref([
  { id: 't1', name: '客户信息表' },
  { id: 't2', name: '贷款明细表' },
  { id: 't3', name: '还款记录表' },
  { id: 't4', name: '产品信息表' },
  { id: 't5', name: '风险评估表' },
])

// 当前分配关系（部门id -> [table ids]）
const deptTableMap = ref([
  { deptId: 'd1', tableIds: ['t1', 't2', 't3'] },
  { deptId: 'd2', tableIds: ['t2', 't4'] },
  { deptId: 'd3', tableIds: ['t3'] },
])

// 编辑区状态
const selectedDepts = ref([])
const selectedTables = ref([])
const isEditing = ref(false)
const editIndex = ref(-1)

// 新增/编辑分配
function handleAddOrEdit() {
  if (!selectedDepts.value.length || !selectedTables.value.length) return
  selectedDepts.value.forEach((deptId) => {
    const idx = deptTableMap.value.findIndex((item) => item.deptId === deptId)
    if (idx !== -1) {
      // 编辑
      deptTableMap.value[idx].tableIds = [...selectedTables.value]
    } else {
      // 新增
      deptTableMap.value.push({ deptId, tableIds: [...selectedTables.value] })
    }
  })
  resetEdit()
}
function handleEdit(row, idx) {
  selectedDepts.value = [row.deptId]
  selectedTables.value = [...row.tableIds]
  isEditing.value = true
  editIndex.value = idx
}
function handleDelete(idx) {
  deptTableMap.value.splice(idx, 1)
  resetEdit()
}
function resetEdit() {
  selectedDepts.value = []
  selectedTables.value = []
  isEditing.value = false
  editIndex.value = -1
}

// 显示用
const deptName = (id) => departments.value.find((d) => d.id === id)?.name || id
const tableNames = (ids) => ids.map((tid) => tables.value.find((t) => t.id === tid)?.name || tid)

// CustomSelect 分组
const deptSelectGroups = [
  { label: '部门列表', options: departments.value.map((d) => ({ value: d.id, label: d.name })) },
]
const tableSelectGroups = [
  { label: '数据表列表', options: tables.value.map((t) => ({ value: t.id, label: t.name })) },
]
</script>

<style scoped>
.data-auth-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.13);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
}
.data-auth-window {
  background: #fff;
  border-radius: 18px;
  box-shadow: 0 8px 40px 0 rgba(0, 32, 72, 0.18);
  width: 1100px;
  height: 750px;
  min-width: 1100px;
  min-height: 750px;
  max-width: 98vw;
  max-height: 98vh;
  padding: 36px 40px 32px 40px;
  display: flex;
  flex-direction: column;
  gap: 18px;
  position: relative;
  box-sizing: border-box;
}
.data-auth-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 18px;
}
.data-auth-header h2 {
  color: #1976d2;
  font-size: 1.5rem;
  font-weight: bold;
  margin: 0;
}
.data-auth-close {
  background: none;
  border: none;
  color: #1976d2;
  font-size: 2.2rem;
  font-weight: bold;
  cursor: pointer;
  transition: color 0.2s;
  margin-left: 18px;
}
.data-auth-close:hover {
  color: #e53935;
}
.data-auth-flex {
  display: flex;
  gap: 32px;
  margin-bottom: 18px;
}
.data-auth-col {
  flex: 1;
  min-width: 180px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.data-auth-label {
  font-size: 15px;
  color: #1976d2;
  font-weight: 600;
  margin-bottom: 4px;
}
.data-auth-btns {
  display: flex;
  gap: 16px;
  margin-bottom: 12px;
}
.data-auth-save-btn {
  background: #1976d2;
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 8px 24px;
  font-size: 15px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s;
}
.data-auth-save-btn:hover {
  background: #1251a3;
}
.data-auth-cancel-btn {
  background: #fff;
  color: #1976d2;
  border: 1.5px solid #1976d2;
  border-radius: 8px;
  padding: 8px 18px;
  font-size: 15px;
  font-weight: 500;
  cursor: pointer;
  transition:
    background 0.2s,
    color 0.2s;
}
.data-auth-cancel-btn:hover {
  background: #eaf3ff;
}
.data-auth-table-section {
  margin-top: 18px;
  width: 100%;
}
.data-auth-table-title {
  font-size: 1.13rem;
  font-weight: 600;
  color: #1976d2;
  margin-bottom: 10px;
  letter-spacing: 1px;
}
.data-auth-table-card {
  background: #fff;
  border-radius: 14px;
  box-shadow: 0 2px 12px 0 rgba(0, 32, 72, 0.08);
  padding: 18px 18px 8px 18px;
  width: 100%;
  overflow-x: auto;
  max-height: 380px;
  overflow-y: auto;
}
.data-auth-table {
  width: 100%;
  min-width: 720px;
  border-collapse: separate;
  border-spacing: 0;
  color: #222;
  font-size: 15px;
  background: transparent;
  display: fixed;
}
.data-auth-table th {
  width: auto;
  background: #f5f8ff;
  color: #1976d2;
  font-weight: 700;
  padding: 14px 12px;
  text-align: left;
  position: sticky;
  top: 0;
  z-index: 2;
  border-bottom: 2px solid #e0e0e0;
  font-size: 15.5px;
}
.data-auth-table td {
  padding: 13px 12px;
  border-bottom: 1px solid #f0f0f0;
  background: #fff;
  font-size: 15px;
  color: #222;
  vertical-align: middle;
}
.data-auth-table tr:nth-child(even) td {
  background: #f7faff;
}
.data-auth-table tr:hover td {
  background: #eaf3ff;
  transition: background 0.2s;
}
.data-auth-edit-btn,
.data-auth-del-btn {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  font-weight: 500;
  border-radius: 6px;
  padding: 4px 14px;
  border: none;
  cursor: pointer;
  transition:
    background 0.2s,
    color 0.2s;
}
.data-auth-edit-btn {
  background: #eaf3ff;
  color: #1976d2;
  margin-right: 8px;
}
.data-auth-edit-btn:hover {
  background: #d0e6ff;
}
.data-auth-del-btn {
  background: #fff0f0;
  color: #e53935;
}
.data-auth-del-btn:hover {
  background: #ffd6d6;
}
.icon-edit {
  font-size: 1.1em;
  margin-right: 2px;
}
.icon-del {
  font-size: 1.1em;
  margin-right: 2px;
}
.data-auth-btn-cell {
  white-space: nowrap;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 0;
}
@media (max-width: 900px) {
  .data-auth-window {
    min-width: 98vw;
    max-width: 98vw;
    width: 98vw;
    height: 98vh;
    min-height: 98vh;
    max-height: 98vh;
  }
  .data-auth-flex {
    flex-direction: column;
    gap: 16px;
  }
  .data-auth-table-card {
    max-height: 38vh;
  }
}
</style>
