<template>
  <div class="error-test-container">
    <h3>错误处理测试</h3>
    <p>点击下面的按钮测试不同类型的错误处理：</p>

    <div class="test-buttons">
      <!-- System Error Tests -->
      <div class="test-group">
        <h4>系统错误 (显示错误遮罩)</h4>
        <button @click="triggerSystemError500" class="test-btn system-error">
          触发 500 服务器错误
        </button>
        <button @click="triggerSystemError502" class="test-btn system-error">
          触发 502 网关错误
        </button>
        <button @click="triggerSystemError503" class="test-btn system-error">
          触发 503 服务不可用
        </button>
        <button @click="triggerNetworkError" class="test-btn system-error">触发网络连接错误</button>
      </div>

      <!-- User Error Tests -->
      <div class="test-group">
        <h4>用户错误 (显示提示消息)</h4>
        <button @click="triggerUserError400" class="test-btn user-error">
          触发 400 请求参数错误
        </button>
        <button @click="triggerUserError401" class="test-btn user-error">触发 401 认证失败</button>
        <button @click="triggerValidationError" class="test-btn user-error">触发验证错误</button>
      </div>

      <!-- Success Tests -->
      <div class="test-group">
        <h4>成功消息 (显示成功提示)</h4>
        <button @click="triggerSuccess" class="test-btn success">触发成功消息</button>
      </div>
    </div>

    <!-- Toast Notification for user errors and success -->
    <ToastNotification
      :show="showNotification"
      :message="notificationMessage"
      :type="notificationType"
      @close="handleToastClose"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { handleError } from '@/utils/errorHandler'
import ToastNotification from '@/components/common/ToastNotification.vue'

// Toast notification state
const showNotification = ref(false)
const notificationMessage = ref('')
const notificationType = ref('success')

const showToast = (message, type = 'success') => {
  notificationMessage.value = message
  notificationType.value = type
  showNotification.value = true
}

const handleToastClose = () => {
  showNotification.value = false
}

// System Error Tests
const triggerSystemError500 = () => {
  const mockError = {
    response: {
      status: 500,
      data: { message: '服务器内部错误' },
    },
  }
  const retryAction = () => {
    console.log('Retrying 500 error...')
    // Simulate retry logic
    return new Promise((resolve) => {
      setTimeout(() => {
        showToast('重试成功！', 'success')
        resolve()
      }, 1000)
    })
  }
  handleError(mockError, '数据处理失败', retryAction, showToast)
}

const triggerSystemError502 = () => {
  const mockError = {
    response: {
      status: 502,
      data: { message: '网关错误' },
    },
  }
  const retryAction = () => {
    console.log('Retrying 502 error...')
    return new Promise((resolve) => {
      setTimeout(() => {
        showToast('重试成功！', 'success')
        resolve()
      }, 1000)
    })
  }
  handleError(mockError, '服务连接失败', retryAction, showToast)
}

const triggerSystemError503 = () => {
  const mockError = {
    response: {
      status: 503,
      data: { message: '服务暂时不可用' },
    },
  }
  const retryAction = () => {
    console.log('Retrying 503 error...')
    return new Promise((resolve) => {
      setTimeout(() => {
        showToast('重试成功！', 'success')
        resolve()
      }, 1000)
    })
  }
  handleError(mockError, '服务暂时不可用', retryAction, showToast)
}

const triggerNetworkError = () => {
  const mockError = {
    request: {},
    // No response indicates network error
  }
  const retryAction = () => {
    console.log('Retrying network error...')
    return new Promise((resolve) => {
      setTimeout(() => {
        showToast('网络连接已恢复！', 'success')
        resolve()
      }, 1000)
    })
  }
  handleError(mockError, '网络连接失败', retryAction, showToast)
}

// User Error Tests
const triggerUserError400 = () => {
  const mockError = {
    response: {
      status: 400,
      data: { message: '请求参数错误' },
    },
  }
  handleError(mockError, '请完善以下必填项：用户名、密码', null, showToast)
}

const triggerUserError401 = () => {
  const mockError = {
    response: {
      status: 401,
      data: { message: '认证失败' },
    },
  }
  handleError(mockError, '用户名或密码错误，请重新输入', null, showToast)
}

const triggerValidationError = () => {
  const mockError = {
    response: {
      status: 422,
      data: { message: '验证失败' },
    },
  }
  handleError(mockError, '请输入有效的邮箱地址', null, showToast)
}

// Success Test
const triggerSuccess = () => {
  handleError(null, '表格已创建成功', null, showToast)
}
</script>

<style scoped>
.error-test-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-buttons {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.test-group {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  background: #f9f9f9;
}

.test-group h4 {
  margin: 0 0 12px 0;
  color: #333;
}

.test-btn {
  margin: 4px 8px 4px 0;
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.test-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.system-error {
  background: #ff4757;
  color: white;
}

.system-error:hover {
  background: #ff3838;
}

.user-error {
  background: #ffa502;
  color: white;
}

.user-error:hover {
  background: #ff9500;
}

.success {
  background: #2ed573;
  color: white;
}

.success:hover {
  background: #26d467;
}

h3 {
  color: #333;
  margin-bottom: 8px;
}

p {
  color: #666;
  margin-bottom: 20px;
}
</style>
