<template>
  <div v-if="charts && charts.length" class="dashboard-charts-grid">
    <div v-for="(chart, idx) in charts" :key="idx" class="dashboard-chart-item">
      <button class="dashboard-chart-delete" @click="handleDelete(idx)" title="删除此图表">
        ×
      </button>
      <ChartVisualizer
        :readonly="true"
        :data="chart.data"
        :columns="chart.columns"
        :initialConfig="chart.config"
        :show="true"
        :sheetName="chart.sheetName"
        style="width: 100%; min-height: 340px"
      />
      <div class="dashboard-chart-title">{{ chart.config.title || '未命名图表' }}</div>
    </div>
  </div>
  <div v-else class="dashboard-empty">
    <div class="dashboard-placeholder">
      <div class="placeholder-text">您可以通过功能区添加内容</div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'
import ChartVisualizer from './common/ChartVisualizer.vue'
const props = defineProps({
  charts: { type: Array, default: () => [] },
})
const emit = defineEmits(['delete-chart'])
function handleDelete(idx) {
  emit('delete-chart', idx)
}
</script>

<style scoped>
.dashboard-charts-grid {
  display: grid;
  grid-template-columns: repeat(3, minmax(480px, 1fr));
  gap: 32px;
  padding: 32px 16px 80px 16px;
  width: 100%;
  max-width: 2200px;
  margin: 0 auto;
}
.dashboard-chart-item {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 24px rgba(25, 118, 210, 0.08);
  padding: 18px 12px 12px 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  min-width: 480px;
  max-width: 700px;
  margin: 0 auto;
}
.dashboard-chart-delete {
  position: absolute;
  top: 8px;
  right: 12px;
  background: #f44336;
  color: #fff;
  border: none;
  border-radius: 50%;
  width: 28px;
  height: 28px;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  z-index: 2;
  box-shadow: 0 2px 8px rgba(244, 67, 54, 0.08);
  transition: background 0.2s;
}
.dashboard-chart-delete:hover {
  background: #d32f2f;
}
.dashboard-chart-title {
  margin-top: 10px;
  font-size: 1.08rem;
  color: #1976d2;
  font-weight: 600;
  text-align: center;
}
.dashboard-empty {
  width: 100%;
  height: 380px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 18px;
}
.dashboard-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 24px;
}
.placeholder-text {
  font-size: 1.2rem;
  color: #1976d2;
  font-weight: 500;
  margin-top: 8px;
}
@media (max-width: 1700px) {
  .dashboard-charts-grid {
    grid-template-columns: repeat(2, minmax(480px, 1fr));
  }
}
@media (max-width: 1100px) {
  .dashboard-charts-grid {
    grid-template-columns: 1fr;
  }
  .dashboard-chart-item {
    min-width: 0;
    max-width: 100vw;
  }
}
</style>
