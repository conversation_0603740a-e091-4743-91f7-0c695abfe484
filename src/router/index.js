import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    { path: '/', component: () => import('../views/HomeView.vue') },
    { path: '/inductive', component: () => import('../views/InductiveView.vue') },
    { path: '/chat', component: () => import('../views/ChatView.vue') },
    { path: '/chat-isd', component: () => import('../views/ChatISDView.vue') },
    { path: '/user-center', component: () => import('../views/UserCenter.vue') },
    { path: '/login', component: () => import('../views/LoginView.vue') },
    {
      path: '/attribution-tasks',
      name: 'TaskView',
      component: () => import('../views/TaskView.vue'),
    },
    {
      path: '/result/:taskId/:instanceId',
      // path: '/result/1/1-1',
      name: 'ResultView',
      component: () => import('../views/ResultView.vue'),
    },
    {
      path: '/admin',
      name: 'AdminConsole',
      component: () => import('../views/AdminConsole.vue'),
    },
  ],
})

export default router
